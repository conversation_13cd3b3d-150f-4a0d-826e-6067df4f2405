import React from 'react';
import { useFormStore } from '../../../stores/formStore';

const ContactInfo: React.FC = () => {
  const { formData, updateFormField, updateNestedFormField } = useFormStore();

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Provide your contact information so visitors can easily reach out to you. This will be displayed in the contact section of your website.
      </p>

      <div className="form-group">
        <label htmlFor="address" className="form-label">
          Business Address
        </label>
        <textarea
          id="address"
          className="input w-full h-24"
          placeholder="e.g., 123 Business Ave, Suite 100, New York, NY 10001"
          value={formData.address}
          onChange={(e) => updateFormField('address', e.target.value)}
        />
      </div>

      <div className="form-group">
        <label htmlFor="phone" className="form-label">
          Phone Number
        </label>
        <input
          type="tel"
          id="phone"
          className="input w-full"
          placeholder="e.g., (*************"
          value={formData.phone}
          onChange={(e) => updateFormField('phone', e.target.value)}
        />
      </div>

      <div className="form-group">
        <label htmlFor="email" className="form-label">
          Email Address <span className="text-error">*</span>
        </label>
        <input
          type="email"
          id="email"
          className="input w-full"
          placeholder="e.g., <EMAIL>"
          value={formData.email}
          onChange={(e) => updateFormField('email', e.target.value)}
          required
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Social Media Profiles</h3>
        <p className="text-sm text-gray-600">
          Add links to your social media profiles to help visitors connect with you on different platforms.
        </p>

        <div className="form-group">
          <label htmlFor="facebook" className="form-label">
            Facebook
          </label>
          <input
            type="url"
            id="facebook"
            className="input w-full"
            placeholder="https://facebook.com/yourcompany"
            value={formData.socialMedia.facebook}
            onChange={(e) => updateNestedFormField('socialMedia', 'facebook', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label htmlFor="instagram" className="form-label">
            Instagram
          </label>
          <input
            type="url"
            id="instagram"
            className="input w-full"
            placeholder="https://instagram.com/yourcompany"
            value={formData.socialMedia.instagram}
            onChange={(e) => updateNestedFormField('socialMedia', 'instagram', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label htmlFor="twitter" className="form-label">
            Twitter
          </label>
          <input
            type="url"
            id="twitter"
            className="input w-full"
            placeholder="https://twitter.com/yourcompany"
            value={formData.socialMedia.twitter}
            onChange={(e) => updateNestedFormField('socialMedia', 'twitter', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label htmlFor="linkedin" className="form-label">
            LinkedIn
          </label>
          <input
            type="url"
            id="linkedin"
            className="input w-full"
            placeholder="https://linkedin.com/company/yourcompany"
            value={formData.socialMedia.linkedin}
            onChange={(e) => updateNestedFormField('socialMedia', 'linkedin', e.target.value)}
          />
        </div>

        <div className="form-group">
          <label htmlFor="youtube" className="form-label">
            YouTube
          </label>
          <input
            type="url"
            id="youtube"
            className="input w-full"
            placeholder="https://youtube.com/c/yourcompany"
            value={formData.socialMedia.youtube}
            onChange={(e) => updateNestedFormField('socialMedia', 'youtube', e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Contact Best Practices</h3>
        <p className="text-sm text-gray-600">
          Including multiple ways for visitors to contact you increases the likelihood of engagement. Your contact information should be easily accessible on your website. Consider adding a contact form in addition to your email and phone number.
        </p>
      </div>
    </div>
  );
};

export default ContactInfo;