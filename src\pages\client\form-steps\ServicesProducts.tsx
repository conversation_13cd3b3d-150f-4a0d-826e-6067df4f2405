import React from 'react';
import { useFormStore } from '../../../stores/formStore';
import { Plus, Trash2, DollarSign, List } from 'lucide-react';

const ServicesProducts: React.FC = () => {
  const { formData, updateFormField } = useFormStore();

  const addOffering = () => {
    updateFormField('offerings', [
      ...formData.offerings,
      {
        name: '',
        description: '',
        price: '',
        features: [],
      },
    ]);
  };

  const removeOffering = (index: number) => {
    updateFormField(
      'offerings',
      formData.offerings.filter((_, i) => i !== index)
    );
  };

  const updateOffering = (index: number, field: string, value: string) => {
    const updatedOfferings = [...formData.offerings];
    updatedOfferings[index] = {
      ...updatedOfferings[index],
      [field]: value,
    };
    updateFormField('offerings', updatedOfferings);
  };

  const addFeature = (index: number) => {
    const updatedOfferings = [...formData.offerings];
    updatedOfferings[index] = {
      ...updatedOfferings[index],
      features: [...(updatedOfferings[index].features || []), ''],
    };
    updateFormField('offerings', updatedOfferings);
  };

  const updateFeature = (offeringIndex: number, featureIndex: number, value: string) => {
    const updatedOfferings = [...formData.offerings];
    if (!updatedOfferings[offeringIndex].features) {
      updatedOfferings[offeringIndex].features = [];
    }
    updatedOfferings[offeringIndex].features![featureIndex] = value;
    updateFormField('offerings', updatedOfferings);
  };

  const removeFeature = (offeringIndex: number, featureIndex: number) => {
    const updatedOfferings = [...formData.offerings];
    updatedOfferings[offeringIndex].features = updatedOfferings[offeringIndex].features?.filter(
      (_, i) => i !== featureIndex
    );
    updateFormField('offerings', updatedOfferings);
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Describe the services or products that your business offers. This information will be featured prominently on your website.
      </p>

      {formData.offerings.map((offering, index) => (
        <div key={index} className="card border bg-gray-50 p-6 space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">
              {offering.name ? offering.name : `Service/Product ${index + 1}`}
            </h3>
            {index > 0 && (
              <button
                type="button"
                onClick={() => removeOffering(index)}
                className="p-1 text-gray-500 hover:text-error"
              >
                <Trash2 size={18} />
              </button>
            )}
          </div>

          <div className="form-group">
            <label htmlFor={`offering-name-${index}`} className="form-label">
              Name <span className="text-error">*</span>
            </label>
            <input
              type="text"
              id={`offering-name-${index}`}
              className="input w-full"
              placeholder="e.g., Website Development, Premium Widget, Consulting Services"
              value={offering.name}
              onChange={(e) => updateOffering(index, 'name', e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor={`offering-description-${index}`} className="form-label">
              Description <span className="text-error">*</span>
            </label>
            <textarea
              id={`offering-description-${index}`}
              className="input w-full h-24"
              placeholder="Describe what this service or product is and what value it provides to customers..."
              value={offering.description}
              onChange={(e) => updateOffering(index, 'description', e.target.value)}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor={`offering-price-${index}`} className="form-label">
              Price (if applicable)
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <DollarSign size={18} className="text-gray-500" />
              </div>
              <input
                type="text"
                id={`offering-price-${index}`}
                className="input w-full pl-10"
                placeholder="e.g., 99.99, From $500, Contact for pricing"
                value={offering.price}
                onChange={(e) => updateOffering(index, 'price', e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="form-label flex items-center">
                <List size={18} className="mr-2" />
                Key Features or Benefits
              </label>
              <button
                type="button"
                onClick={() => addFeature(index)}
                className="text-sm text-primary hover:text-primary-dark flex items-center"
              >
                <Plus size={16} className="mr-1" /> Add Feature
              </button>
            </div>

            {offering.features && offering.features.length > 0 ? (
              <div className="space-y-2">
                {offering.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center">
                    <input
                      type="text"
                      className="input flex-grow"
                      placeholder="e.g., 24/7 Support, Free shipping, 1-year warranty"
                      value={feature}
                      onChange={(e) =>
                        updateFeature(index, featureIndex, e.target.value)
                      }
                    />
                    <button
                      type="button"
                      onClick={() => removeFeature(index, featureIndex)}
                      className="ml-2 p-1 text-gray-500 hover:text-error"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">
                No features added yet. Features help highlight the benefits of your offering.
              </p>
            )}
          </div>
        </div>
      ))}

      <button
        type="button"
        onClick={addOffering}
        className="btn btn-outline w-full flex items-center justify-center"
      >
        <Plus size={18} className="mr-2" /> Add Another Service/Product
      </button>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Presentation Tips</h3>
        <p className="text-sm text-gray-600">
          Be clear and specific about what your offerings include. Use benefit-oriented language that explains how your services or products solve problems for your customers. Including pricing information (even if it's just "Starting at...") can help qualify leads.
        </p>
      </div>
    </div>
  );
};

export default ServicesProducts;