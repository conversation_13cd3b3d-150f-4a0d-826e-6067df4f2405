import React, { useEffect, useState } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import AdminLayout from '../../components/layout/AdminLayout';
import {
  Users,
  FileText,
  CheckCircle,
  Clock,
  BarChart3,
  AlertTriangle,
  TrendingUp,
  Calendar,
  ArrowUpRight,
  Activity,
  PieChart
} from 'lucide-react';

// Import our new UI components
import Chart from '../../components/ui/Chart';
import ProgressBar from '../../components/ui/ProgressBar';
import StatCard from '../../components/ui/StatCard';

interface DashboardStats {
  totalClients: number;
  totalProjects: number;
  projectsCompleted: number;
  projectsInProgress: number;
  projectsInReview: number;
  activeTodayCount: number;
}

interface RecentProject {
  id: string;
  name: string;
  status: string;
  progress: number;
  updated_at: string;
  user_email: string;
}

const AdminDashboard: React.FC = () => {
  const { supabase } = useSupabase();
  const [stats, setStats] = useState<DashboardStats>({
    totalClients: 0,
    totalProjects: 0,
    projectsCompleted: 0,
    projectsInProgress: 0,
    projectsInReview: 0,
    activeTodayCount: 0,
  });
  const [recentProjects, setRecentProjects] = useState<RecentProject[]>([]);
  const [loading, setLoading] = useState(true);



  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Get total clients
      const { count: clientCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'client');

      // Get project statistics
      const { data: projects } = await supabase
        .from('projects')
        .select('id, status')
        .order('created_at', { ascending: false });

      // Calculate project statistics
      const totalProjects = projects?.length || 0;
      const projectsCompleted = projects?.filter(p => p.status === 'completed').length || 0;
      const projectsInProgress = projects?.filter(p => p.status === 'in_progress').length || 0;
      const projectsInReview = projects?.filter(p => p.status === 'review').length || 0;

      // Get active today count (projects updated today)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const { count: activeTodayCount } = await supabase
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .gte('updated_at', today.toISOString());

      // Get recent projects with user email
      const { data: recentProjectsData } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          status,
          progress,
          updated_at,
          users!projects_user_id_fkey (
            email
          )
        `)
        .order('updated_at', { ascending: false })
        .limit(5);

      const formattedRecentProjects = recentProjectsData?.map(project => ({
        id: project.id,
        name: project.name,
        status: project.status,
        progress: project.progress,
        updated_at: project.updated_at,
        user_email: project.users?.email || 'Unknown',
      })) || [];

      setStats({
        totalClients: clientCount || 0,
        totalProjects,
        projectsCompleted,
        projectsInProgress,
        projectsInReview,
        activeTodayCount: activeTodayCount || 0,
      });

      setRecentProjects(formattedRecentProjects);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Clock className="h-5 w-5 text-gray-500" />;
      case 'in_progress':
        return <AlertTriangle className="h-5 w-5 text-warning" />;
      case 'review':
        return <AlertTriangle className="h-5 w-5 text-secondary" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'in_progress':
        return 'In Progress';
      case 'review':
        return 'Under Review';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  };

  // Generate some sample data for charts
  const generateChartData = () => {
    return Array.from({ length: 12 }, () => Math.floor(Math.random() * 100) + 20);
  };

  const monthlyData = generateChartData();
  const monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Calculate completion rate percentage
  const completionRate = stats.totalProjects > 0
    ? Math.round((stats.projectsCompleted / stats.totalProjects) * 100)
    : 0;

  return (
    <AdminLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Admin Dashboard</h1>
          <p className="mt-1 text-gray-500">
            Overview of website creation projects and client activity.
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <StatCard
                title="Total Clients"
                value={stats.totalClients}
                icon={Users}
                color="primary"
                trend={5}
                trendLabel="vs last month"
              />

              <StatCard
                title="Total Projects"
                value={stats.totalProjects}
                icon={FileText}
                color="secondary"
              />

              <StatCard
                title="Completed"
                value={stats.projectsCompleted}
                icon={CheckCircle}
                color="success"
                trend={8}
                trendLabel="completion rate"
              />

              <StatCard
                title="In Progress"
                value={stats.projectsInProgress + stats.projectsInReview}
                icon={Clock}
                color="warning"
              />
            </div>

            {/* Charts and Activity Overview */}
            <div className="grid gap-6 lg:grid-cols-2">
              <div className="card-gradient">
                <div className="chart-header">
                  <div>
                    <h3 className="chart-title">Monthly Overview</h3>
                    <p className="text-sm text-gray-500">Website creation activity</p>
                  </div>
                  <div className="flex items-center text-sm text-primary">
                    <span className="font-medium">View Report</span>
                    <ArrowUpRight className="ml-1 h-4 w-4" />
                  </div>
                </div>
                <Chart
                  data={monthlyData}
                  labels={monthLabels}
                  height={180}
                  color="primary"
                  showLabels={true}
                />
              </div>

              <div className="card-gradient">
                <div className="chart-header">
                  <div>
                    <h3 className="chart-title">Project Status</h3>
                    <p className="text-sm text-gray-500">Current distribution</p>
                  </div>
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-secondary/10">
                    <PieChart className="h-4 w-4 text-secondary" />
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <ProgressBar
                    value={stats.totalProjects - stats.projectsCompleted - stats.projectsInProgress - stats.projectsInReview}
                    max={stats.totalProjects}
                    color="primary"
                    label="Draft"
                    showValue={false}
                  />

                  <ProgressBar
                    value={stats.projectsInProgress}
                    max={stats.totalProjects}
                    color="warning"
                    label="In Progress"
                    showValue={false}
                  />

                  <ProgressBar
                    value={stats.projectsInReview}
                    max={stats.totalProjects}
                    color="secondary"
                    label="Under Review"
                    showValue={false}
                  />

                  <ProgressBar
                    value={stats.projectsCompleted}
                    max={stats.totalProjects}
                    color="success"
                    label="Completed"
                    showValue={false}
                  />
                </div>
              </div>
            </div>

            {/* Activity and Stats */}
            <div className="grid gap-6 lg:grid-cols-3">
              <div className="card-gradient lg:col-span-1">
                <div className="chart-header">
                  <h3 className="chart-title">Recent Activity</h3>
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                    <Activity className="h-4 w-4 text-primary" />
                  </div>
                </div>

                <div className="mt-4 space-y-4">
                  <div className="flex items-center rounded-xl bg-secondary/5 p-4">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-xl bg-secondary/10">
                      <Calendar className="h-5 w-5 text-secondary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Today's Active Projects</h3>
                      <p className="text-sm text-gray-600">
                        {stats.activeTodayCount} projects updated today
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center rounded-xl bg-success/5 p-4">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-xl bg-success/10">
                      <CheckCircle className="h-5 w-5 text-success" />
                    </div>
                    <div>
                      <h3 className="font-medium">Projects Ready for Review</h3>
                      <p className="text-sm text-gray-600">
                        {stats.projectsInReview} projects waiting for review
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center rounded-xl bg-primary/5 p-4">
                    <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-xl bg-primary/10">
                      <TrendingUp className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium">Completion Rate</h3>
                      <p className="text-sm text-gray-600">
                        {completionRate}% of all projects completed
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card-gradient lg:col-span-2">
                <div className="chart-header">
                  <div>
                    <h3 className="chart-title">Quarterly Performance</h3>
                    <p className="text-sm text-gray-500">Website completion trends</p>
                  </div>
                  <div className="flex items-center text-sm text-primary">
                    <span className="font-medium">Q3 2023</span>
                  </div>
                </div>

                <div className="mt-4 grid grid-cols-2 gap-4">
                  <div className="rounded-xl bg-primary/5 p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-500">Avg. Completion Time</span>
                      <span className="text-xs font-medium text-success">↓ 12%</span>
                    </div>
                    <p className="mt-2 text-2xl font-bold text-gray-900">14 days</p>
                  </div>

                  <div className="rounded-xl bg-secondary/5 p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-500">Client Satisfaction</span>
                      <span className="text-xs font-medium text-success">↑ 8%</span>
                    </div>
                    <p className="mt-2 text-2xl font-bold text-gray-900">94%</p>
                  </div>

                  <div className="rounded-xl bg-success/5 p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-500">Completion Rate</span>
                      <span className="text-xs font-medium text-success">↑ 5%</span>
                    </div>
                    <p className="mt-2 text-2xl font-bold text-gray-900">{completionRate}%</p>
                  </div>

                  <div className="rounded-xl bg-warning/5 p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-500">New Clients</span>
                      <span className="text-xs font-medium text-success">↑ 15%</span>
                    </div>
                    <p className="mt-2 text-2xl font-bold text-gray-900">+{Math.round(stats.totalClients * 0.15)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Projects */}
            <div className="card-gradient">
              <div className="chart-header">
                <h3 className="chart-title">Recent Projects</h3>
                <div className="flex items-center text-sm text-primary">
                  <span className="font-medium">View All</span>
                  <ArrowUpRight className="ml-1 h-4 w-4" />
                </div>
              </div>

              {recentProjects.length > 0 ? (
                <div className="mt-4 overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-100 rounded-xl">
                    <thead>
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                        >
                          Project
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                        >
                          Client
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                        >
                          Status
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                        >
                          Progress
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                        >
                          Last Updated
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100 bg-white">
                      {recentProjects.map((project) => (
                        <tr key={project.id} className="transition-colors hover:bg-gray-50">
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="font-medium text-gray-900">{project.name}</div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {project.user_email}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="flex items-center">
                              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                project.status === 'completed'
                                  ? 'bg-success/10 text-success'
                                  : project.status === 'review'
                                  ? 'bg-secondary/10 text-secondary'
                                  : project.status === 'in_progress'
                                  ? 'bg-warning/10 text-warning'
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {getStatusIcon(project.status)}
                                <span className="ml-1">{getStatusText(project.status)}</span>
                              </span>
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4">
                            <div className="w-full max-w-xs">
                              <ProgressBar
                                value={project.progress}
                                max={100}
                                color={
                                  project.progress === 100
                                    ? 'success'
                                    : project.progress > 50
                                    ? 'secondary'
                                    : 'primary'
                                }
                                size="sm"
                                showValue={true}
                              />
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                            {formatDate(project.updated_at)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="mt-4 flex justify-center rounded-xl bg-gray-50 py-8 text-gray-500">
                  No projects to display
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;