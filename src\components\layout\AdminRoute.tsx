import React, { useState, useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const AdminRoute: React.FC = () => {
  const { user, loading, isAdmin, userRole } = useAuth();
  const [roleChecked, setRoleChecked] = useState(false);
  const [isUserAdmin, setIsUserAdmin] = useState(false);
  const [initialRender, setInitialRender] = useState(true);
  const location = useLocation();

  // Effect to handle role checking with a slight delay to ensure role is loaded
  useEffect(() => {
    if (!loading && user) {
      // First check using the isAdmin function
      const adminStatus = isAdmin();

      // If not admin, check sessionStorage as a fallback
      if (!adminStatus) {
        const storedRole = sessionStorage.getItem('userRole');

        if (storedRole === 'admin') {
          setIsUserAdmin(true);
        } else {
          setIsUserAdmin(false);
        }
      } else {
        setIsUserAdmin(true);
      }

      // Mark role as checked
      setRoleChecked(true);
    }
  }, [loading, user, userRole, isAdmin]);

  // Effect to handle the initial render
  useEffect(() => {
    // After the component has been rendered once, set initialRender to false
    if (initialRender) {
      // Force the admin role to be checked again after a short delay
      const timer = setTimeout(() => {
        // Check sessionStorage directly
        const storedRole = sessionStorage.getItem('userRole');
        if (storedRole === 'admin') {
          setIsUserAdmin(true);
        }
        setInitialRender(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [initialRender]);

  // Show loading spinner while authentication is in progress or role is being checked
  if (loading || (user && !roleChecked) || initialRender) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  // Redirect to login if no user
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check sessionStorage one more time before redirecting
  const storedRole = sessionStorage.getItem('userRole');

  // Redirect to client dashboard if user is not an admin
  if (!isUserAdmin && storedRole !== 'admin') {
    return <Navigate to="/client/dashboard" replace />;
  }

  // User is authenticated and is an admin
  return <Outlet />;
};

export default AdminRoute;