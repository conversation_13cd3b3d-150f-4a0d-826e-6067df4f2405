import React from 'react';
import { useFormStore } from '../../../stores/formStore';

const BusinessDetails: React.FC = () => {
  const { formData, updateFormField } = useFormStore();

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Share the core values and purpose of your business. This information will help visitors understand what makes your company unique.
      </p>

      <div className="form-group">
        <label htmlFor="missionStatement" className="form-label">
          Mission Statement
        </label>
        <textarea
          id="missionStatement"
          className="input w-full h-32"
          placeholder="e.g., Our mission is to provide innovative solutions that improve lives while maintaining environmental sustainability."
          value={formData.missionStatement}
          onChange={(e) => updateFormField('missionStatement', e.target.value)}
        />
        <p className="mt-1 text-xs text-gray-500">
          Your mission statement should explain your company's purpose and goals in a clear, concise way.
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="valueProposition" className="form-label">
          Unique Value Proposition <span className="text-error">*</span>
        </label>
        <textarea
          id="valueProposition"
          className="input w-full h-32"
          placeholder="e.g., Unlike other companies, we offer personalized service with guaranteed 24-hour response times and eco-friendly options at competitive prices."
          value={formData.valueProposition}
          onChange={(e) => updateFormField('valueProposition', e.target.value)}
          required
        />
        <p className="mt-1 text-xs text-gray-500">
          Explain what sets you apart from competitors and why customers should choose your business.
        </p>
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Writing Tips</h3>
        <p className="text-sm text-gray-600">
          <strong>Mission Statement:</strong> Keep it under 100 words. Focus on what you do, who you serve, and why it matters.
        </p>
        <p className="text-sm text-gray-600 mt-2">
          <strong>Value Proposition:</strong> Be specific about the benefits you provide. Consider the format:
          "We help [target customer] do [problem you solve] by [how you're different] which results in [key benefit]."
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Example Value Propositions:</h3>
        <ul className="text-sm text-gray-600 space-y-2 list-disc ml-5">
          <li>"We help small business owners reduce tax liability by providing year-round strategic planning, not just tax-time preparation."</li>
          <li>"Our fitness studio combines personalized training plans with nutritional guidance and community support, helping busy professionals achieve lasting results in half the time."</li>
          <li>"Unlike mass-produced furniture stores, our handcrafted pieces are built to last generations using sustainable materials, while still remaining affordable for middle-income families."</li>
        </ul>
      </div>
    </div>
  );
};

export default BusinessDetails;