import React from 'react';
import { Check } from 'lucide-react';

interface MessageStatusProps {
  read: boolean;
}

const MessageStatus: React.FC<MessageStatusProps> = ({ read }) => {
  return (
    <span className="inline-flex items-center ml-1">
      {read ? (
        // Double check for read messages (pink color)
        <div className="relative flex" style={{ width: '14px', height: '12px' }}>
          <Check
            className="h-3 w-3 text-primary absolute"
            style={{
              left: '-4px',
              strokeWidth: 3,
              transform: 'scale(0.9) rotate(-4deg)'
            }}
          />
          <Check
            className="h-3 w-3 text-primary absolute"
            style={{
              left: '2px',
              strokeWidth: 3,
              transform: 'scale(0.9) rotate(-4deg)'
            }}
          />
        </div>
      ) : (
        // Single check for sent but unread messages
        <Check
          className="h-3 w-3 text-gray-500"
          style={{
            strokeWidth: 3,
            transform: 'scale(0.9) rotate(-4deg)'
          }}
        />
      )}
    </span>
  );
};

export default MessageStatus;
