import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useMessaging } from '../../contexts/MessagingContext';
import NotificationBadge from '../messaging/NotificationBadge';
import AvatarUpload from '../profile/AvatarUpload';
import {
  Layout,
  Home,
  FileText,
  MessageSquare,
  LogOut,
  Menu,
  X,
  User,
  Bell,
  Search,
  Plus
} from 'lucide-react';

interface ClientLayoutProps {
  children: React.ReactNode;
}

const ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {
  const { signOut, user, profile } = useAuth();
  const { unreadCount } = useMessaging();
  const location = useLocation();
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Get display name for the user
  const displayName = profile ?
    (profile.client_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Client User') :
    'Client User';

  const handleSignOut = async () => {
    await signOut();
    navigate('/login');
  };

  const navigation = [
    { name: 'Dashboard', href: '/client/dashboard', icon: Home },
    { name: 'Website Form', href: '/client/form', icon: FileText },
    { name: 'Messages', href: '/client/messages', icon: MessageSquare },
    { name: 'Profile', href: '/client/profile', icon: User }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile menu */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between border-b border-gray-100 bg-white px-4 py-3 shadow-sm">
          <Link to="/" className="flex items-center">
            <Layout className="mr-2 h-6 w-6 text-primary" />
            <span className="text-lg font-semibold text-primary">InstaSite</span>
          </Link>
          <div className="flex items-center space-x-3">
            <Link to="/client/messages" className="btn-icon relative">
              <Bell className="h-5 w-5" />
              {unreadCount > 0 && <NotificationBadge count={unreadCount} pulse={true} />}
            </Link>
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="btn-icon"
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>

        {mobileMenuOpen && (
          <div className="border-b border-gray-100 bg-white shadow-sm">
            <div className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="w-full rounded-xl border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                />
              </div>
            </div>
            <nav className="space-y-1 px-2 py-3">
              {navigation.map((item) => {
                const isActive = location.pathname.startsWith(item.href);
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center rounded-xl px-3 py-2.5 text-sm font-medium transition-all ${
                      isActive
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <div className="relative flex items-center">
                      <div className="relative">
                        <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                      </div>
                      <span>{item.name}</span>
                      {item.name === 'Messages' && unreadCount > 0 && (
                        <div className="ml-2 -mt-1">
                          <NotificationBadge count={unreadCount} className="static" pulse={true} />
                        </div>
                      )}
                    </div>
                  </Link>
                );
              })}
              <button
                onClick={handleSignOut}
                className="flex w-full items-center rounded-xl px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-primary transition-all"
              >
                <LogOut className="mr-3 h-5 w-5 flex-shrink-0" />
                Sign&nbsp;Out
              </button>
            </nav>
          </div>
        )}
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-grow flex-col overflow-y-auto rounded-r-2xl bg-white shadow-elevated">
          <div className="flex h-16 flex-shrink-0 items-center px-6">
            <Link to="/" className="flex items-center">
              <Layout className="mr-2 h-7 w-7 text-primary" />
              <span className="text-xl font-semibold text-primary">InstaSite</span>
            </Link>
          </div>
          <div className="px-4 mt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full rounded-xl border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              />
            </div>
          </div>
          <div className="mt-6 flex flex-grow flex-col">
            <nav className="flex-1 space-y-1 px-3 pb-4">
              {navigation.map((item) => {
                const isActive = location.pathname.startsWith(item.href);
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center rounded-xl px-3 py-2.5 text-sm font-medium transition-all ${
                      isActive
                        ? 'bg-primary/10 text-primary'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
                    }`}
                  >
                    <div className="relative flex items-center">
                      <div className="relative">
                        <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                      </div>
                      <span>{item.name}</span>
                      {item.name === 'Messages' && unreadCount > 0 && (
                        <div className="ml-2 -mt-1">
                          <NotificationBadge count={unreadCount} className="static" pulse={true} />
                        </div>
                      )}
                    </div>
                  </Link>
                );
              })}
            </nav>
          </div>
          <div className="mx-3 mb-3 flex flex-col rounded-xl bg-gray-50 p-4">
            <div className="flex items-center">
              <Link to="/client/profile" className="flex-shrink-0">
                {profile && user ? (
                  <div className="h-10 w-10 overflow-hidden rounded-full">
                    {profile.avatar_url ? (
                      <img
                        src={profile.avatar_url}
                        alt="Profile"
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-white">
                        <User className="h-5 w-5" />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary text-white">
                    <User className="h-5 w-5" />
                  </div>
                )}
              </Link>
              <div className="ml-3">
                <Link to="/client/profile" className="text-sm font-medium text-gray-900 hover:text-primary">
                  {displayName}
                </Link>
              </div>
            </div>
            <div className="mt-3 flex justify-between items-center border-t border-gray-200 pt-3">
              <button
                onClick={handleSignOut}
                className="flex items-center text-xs text-gray-500 hover:text-primary"
              >
                <LogOut className="mr-1.5 h-3.5 w-3.5" />
                Sign out
              </button>
              <span className="text-xs text-gray-400">Funky Invoice</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top header */}
        <header className="sticky top-0 z-10 bg-white shadow-sm">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            <h1 className="text-xl font-semibold text-gray-900">
              {navigation.find(item => location.pathname.startsWith(item.href))?.name || 'Dashboard'}
            </h1>
            <div className="flex items-center space-x-4">
              <Link to="/client/form" className="btn btn-primary flex items-center text-sm">
                <Plus className="mr-1 h-4 w-4" /> New Website
              </Link>
              <Link to="/client/messages" className="btn-icon relative">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && <NotificationBadge count={unreadCount} pulse={true} />}
              </Link>
              <div className="h-6 w-px bg-gray-200"></div>
              <Link to="/client/profile" className="flex items-center">
                {profile && user ? (
                  <div className="h-8 w-8 overflow-hidden rounded-full">
                    {profile.avatar_url ? (
                      <img
                        src={profile.avatar_url}
                        alt="Profile"
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
                        <User className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
                    <User className="h-4 w-4" />
                  </div>
                )}
              </Link>
            </div>
          </div>
        </header>

        <main className="py-6 px-4 sm:px-6 lg:px-8">{children}</main>
      </div>
    </div>
  );
};

export default ClientLayout;