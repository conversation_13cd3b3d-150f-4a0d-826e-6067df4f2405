import React, { useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useToast, ToastProvider } from '../../components/ui/Toaster';
import AdminLayout from '../../components/layout/AdminLayout';
import ProfileForm from '../../components/profile/ProfileForm';
import { Shield, Info, Key } from 'lucide-react';

const ProfileContent: React.FC = () => {
  const { user, profile, updateProfile, refreshProfile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    // Refresh profile data when component mounts
    refreshProfile();
  }, [refreshProfile]);

  const handleProfileUpdate = async (data: {
    first_name: string;
    last_name: string;
    avatar_url: string;
  }) => {
    try {
      const { error } = await updateProfile(data);
      
      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  if (!user || !profile) {
    return (
      <AdminLayout>
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto mb-4 h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-gray-500">Loading profile information...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Admin Profile</h1>
          <p className="mt-1 text-gray-500">
            Manage your account information and preferences
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <ProfileForm
              userId={user.id}
              email={profile.email}
              firstName={profile.first_name}
              lastName={profile.last_name}
              clientName={null}
              avatarUrl={profile.avatar_url}
              isAdmin={true}
              onProfileUpdate={handleProfileUpdate}
            />
          </div>

          <div className="space-y-6">
            <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
              <div className="mb-4 flex items-center text-primary">
                <Shield className="mr-2 h-5 w-5" />
                <h3 className="font-semibold">Account Security</h3>
              </div>
              <p className="mb-4 text-sm text-gray-600">
                As an administrator, your account has elevated privileges. Ensure you maintain strong security practices.
              </p>
              <button
                className="btn btn-outline w-full"
                onClick={() => toast('Password reset functionality coming soon!', 'info')}
              >
                Change Password
              </button>
            </div>

            <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
              <div className="mb-4 flex items-center text-amber-600">
                <Key className="mr-2 h-5 w-5" />
                <h3 className="font-semibold">Admin Privileges</h3>
              </div>
              <p className="text-sm text-gray-600">
                Your admin account allows you to:
              </p>
              <ul className="mt-2 space-y-1 text-sm text-gray-600">
                <li>• Manage all client accounts</li>
                <li>• Access and modify all projects</li>
                <li>• Generate website prompts</li>
                <li>• Communicate with all clients</li>
              </ul>
            </div>

            <div className="rounded-xl border border-blue-100 bg-blue-50 p-6">
              <div className="mb-2 flex items-center text-blue-600">
                <Info className="mr-2 h-5 w-5" />
                <h3 className="font-semibold">Profile Tips</h3>
              </div>
              <ul className="space-y-2 text-sm text-blue-700">
                <li>• Adding a profile picture helps clients identify you</li>
                <li>• Your name will be visible to clients in messages</li>
                <li>• Keep your contact information up to date</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

const AdminProfile: React.FC = () => {
  return (
    <ToastProvider>
      <ProfileContent />
    </ToastProvider>
  );
};

export default AdminProfile;
