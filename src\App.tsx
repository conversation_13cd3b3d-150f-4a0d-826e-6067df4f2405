import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster, ToastProvider } from './components/ui/Toaster';
import { AuthProvider } from './contexts/AuthContext';
import { SupabaseProvider } from './contexts/SupabaseContext';
import { MessagingProvider } from './contexts/MessagingContext';

// Pages
import Login from './pages/Login';
import Register from './pages/Register';
import ClientDashboard from './pages/client/Dashboard';
import ClientForm from './pages/client/Form';
import ClientMessages from './pages/client/Messages';
import ClientProfile from './pages/client/Profile';
import AdminDashboard from './pages/admin/Dashboard';
import AdminMessages from './pages/admin/Messages';
import AdminProfile from './pages/admin/Profile';
import ClientManagement from './pages/admin/ClientManagement';
import ProjectManagement from './pages/admin/ProjectManagement';
import PromptGenerator from './pages/admin/PromptGenerator';
import Landing from './pages/Landing';

// Layout Components
import ProtectedRoute from './components/layout/ProtectedRoute';
import AdminRoute from './components/layout/AdminRoute';

function App() {
  return (
    <SupabaseProvider>
      <AuthProvider>
        <MessagingProvider>
          <ToastProvider>
            <Router>
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<Landing />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />

                {/* Protected client routes */}
                <Route element={<ProtectedRoute />}>
                  <Route path="/client/dashboard" element={<ClientDashboard />} />
                  <Route path="/client/form/:step?" element={<ClientForm />} />
                  <Route path="/client/messages" element={<ClientMessages />} />
                  <Route path="/client/profile" element={<ClientProfile />} />
                </Route>

                {/* Protected admin routes */}
                <Route element={<AdminRoute />}>
                  <Route path="/admin/dashboard" element={<AdminDashboard />} />
                  <Route path="/admin/clients" element={<ClientManagement />} />
                  <Route path="/admin/projects" element={<ProjectManagement />} />
                  <Route path="/admin/prompts" element={<PromptGenerator />} />
                  <Route path="/admin/messages" element={<AdminMessages />} />
                  <Route path="/admin/profile" element={<AdminProfile />} />
                </Route>

                {/* Fallback route */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Router>
            <Toaster />
          </ToastProvider>
        </MessagingProvider>
      </AuthProvider>
    </SupabaseProvider>
  );
}

export default App;