import React, { useEffect, useState, memo } from 'react';

interface NotificationBadgeProps {
  count: number;
  className?: string;
  pulse?: boolean;
}

// Using memo to prevent unnecessary re-renders
const NotificationBadge: React.FC<NotificationBadgeProps> = memo(({
  count,
  className = '',
  pulse = true
}) => {
  const [animate, setAnimate] = useState(false);
  const [displayCount, setDisplayCount] = useState(count);

  // Update display count when count changes, with a minimum display time
  useEffect(() => {
    // If count increases, update immediately
    if (count > displayCount) {
      setDisplayCount(count);
    }
    // If count decreases or goes to zero, add a small delay to prevent flickering
    else if (count < displayCount) {
      // Keep showing for at least 5 seconds before removing
      const timer = setTimeout(() => {
        setDisplayCount(count);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [count, displayCount]);

  useEffect(() => {
    // Trigger animation when count changes
    if (count > 0) {
      setAnimate(true);
      const timer = setTimeout(() => setAnimate(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [count]);

  // Don't render anything if count is 0
  if (displayCount <= 0) return null;

  return (
    <div
      className={`
        ${className !== 'static' ? 'absolute -right-1 -top-1' : ''}
        flex h-5 min-w-5 items-center justify-center
        rounded-full bg-error px-1.5
        text-xs font-medium text-white
        shadow-sm
        ${animate ? 'scale-110' : ''}
        ${pulse ? 'animate-pulse-subtle' : ''}
        transition-all duration-300
        ${className}
      `}
    >
      {displayCount > 99 ? '99+' : displayCount}
    </div>
  );
});

// Add display name for debugging
NotificationBadge.displayName = 'NotificationBadge';

export default NotificationBadge;
