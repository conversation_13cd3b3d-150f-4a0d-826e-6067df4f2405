import React from 'react';

interface ChartProps {
  data: number[];
  labels?: string[];
  height?: number;
  color?: string;
  gradient?: boolean;
  showLabels?: boolean;
  title?: string;
  description?: string;
  value?: string | number;
  percentage?: number;
  isPositive?: boolean;
}

const Chart: React.FC<ChartProps> = ({
  data,
  labels,
  height = 100,
  color = 'primary',
  gradient = true,
  showLabels = false,
  title,
  description,
  value,
  percentage,
  isPositive = true,
}) => {
  // Find the max value to normalize the chart
  const maxValue = Math.max(...data);
  
  // Generate the chart points
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 100 - (value / maxValue) * 100;
    return `${x},${y}`;
  }).join(' ');

  // Generate the gradient ID
  const gradientId = `chart-gradient-${Math.random().toString(36).substring(2, 9)}`;

  // Get the color class based on the color prop
  const getColorClass = () => {
    switch (color) {
      case 'primary':
        return 'text-primary';
      case 'secondary':
        return 'text-secondary';
      case 'success':
        return 'text-success';
      case 'warning':
        return 'text-warning';
      case 'accent':
        return 'text-accent';
      default:
        return 'text-primary';
    }
  };

  // Get the gradient colors based on the color prop
  const getGradientColors = () => {
    switch (color) {
      case 'primary':
        return { start: '#E83E8C', end: '#F9A8D4' };
      case 'secondary':
        return { start: '#6F42C1', end: '#C4B5FD' };
      case 'success':
        return { start: '#20C997', end: '#4DD4AC' };
      case 'warning':
        return { start: '#FD7E14', end: '#FD9A47' };
      case 'accent':
        return { start: '#6610F2', end: '#8540F5' };
      default:
        return { start: '#E83E8C', end: '#F9A8D4' };
    }
  };

  const { start, end } = getGradientColors();
  const colorClass = getColorClass();

  return (
    <div className="chart-container">
      {(title || value) && (
        <div className="chart-header">
          <div>
            {title && <h3 className="chart-title">{title}</h3>}
            {description && <p className="text-sm text-gray-500">{description}</p>}
          </div>
          <div className="text-right">
            {value && <div className={`text-2xl font-bold ${colorClass}`}>{value}</div>}
            {percentage !== undefined && (
              <div className={`text-sm font-medium ${isPositive ? 'text-success' : 'text-error'}`}>
                {isPositive ? '↑' : '↓'} {percentage}%
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="relative" style={{ height: `${height}px` }}>
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          {gradient && (
            <defs>
              <linearGradient id={gradientId} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={start} stopOpacity="0.2" />
                <stop offset="100%" stopColor={end} stopOpacity="0" />
              </linearGradient>
            </defs>
          )}
          
          {/* Area under the line */}
          {gradient && (
            <polygon
              points={`0,100 ${points} 100,100`}
              fill={`url(#${gradientId})`}
            />
          )}
          
          {/* The line itself */}
          <polyline
            points={points}
            fill="none"
            stroke={start}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {data.map((value, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 100 - (value / maxValue) * 100;
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="1.5"
                fill="#fff"
                stroke={start}
                strokeWidth="1"
              />
            );
          })}
        </svg>
        
        {/* X-axis labels */}
        {showLabels && labels && (
          <div className="flex justify-between mt-2 px-2 text-xs text-gray-500">
            {labels.map((label, index) => (
              <div key={index}>{label}</div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Chart;
