export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          role: 'client' | 'admin';
          first_name: string | null;
          last_name: string | null;
          client_name: string | null;
          avatar_url: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          role?: 'client' | 'admin';
          first_name?: string | null;
          last_name?: string | null;
          client_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          role?: 'client' | 'admin';
          first_name?: string | null;
          last_name?: string | null;
          client_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
        };
      };
      projects: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          status: 'draft' | 'in_progress' | 'review' | 'completed';
          progress: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          status?: 'draft' | 'in_progress' | 'review' | 'completed';
          progress?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          status?: 'draft' | 'in_progress' | 'review' | 'completed';
          progress?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      form_submissions: {
        Row: {
          id: string;
          project_id: string;
          step: string;
          data: Json;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          step: string;
          data: Json;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          step?: string;
          data?: Json;
          created_at?: string;
          updated_at?: string;
        };
      };
      messages: {
        Row: {
          id: string;
          project_id: string;
          sender_id: string;
          content: string;
          read: boolean;
          created_at: string;
          reactions?: Json;
        };
        Insert: {
          id?: string;
          project_id: string;
          sender_id: string;
          content: string;
          read?: boolean;
          created_at?: string;
          reactions?: Json;
        };
        Update: {
          id?: string;
          project_id?: string;
          sender_id?: string;
          content?: string;
          read?: boolean;
          created_at?: string;
          reactions?: Json;
        };
      };
      prompts: {
        Row: {
          id: string;
          project_id: string;
          content: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          content: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          content?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
};