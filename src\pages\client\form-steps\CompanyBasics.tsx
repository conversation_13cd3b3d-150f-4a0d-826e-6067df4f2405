import React from 'react';
import { useFormStore } from '../../../stores/formStore';

const CompanyBasics: React.FC = () => {
  const { formData, updateFormField } = useFormStore();

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Tell us about the basic information of your company. This will help us establish your brand identity on the website.
      </p>

      <div className="form-group">
        <label htmlFor="companyName" className="form-label">
          Company Name <span className="text-error">*</span>
        </label>
        <input
          type="text"
          id="companyName"
          className="input w-full"
          placeholder="e.g., Acme Corporation"
          value={formData.companyName}
          onChange={(e) => updateFormField('companyName', e.target.value)}
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="industry" className="form-label">
          Industry <span className="text-error">*</span>
        </label>
        <input
          type="text"
          id="industry"
          className="input w-full"
          placeholder="e.g., Technology, Healthcare, Retail"
          value={formData.industry}
          onChange={(e) => updateFormField('industry', e.target.value)}
          required
        />
        <p className="mt-1 text-xs text-gray-500">
          Knowing your industry helps us style your website appropriately
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="foundingYear" className="form-label">
          Year Founded
        </label>
        <input
          type="text"
          id="foundingYear"
          className="input w-full"
          placeholder="e.g., 2010"
          value={formData.foundingYear}
          onChange={(e) => updateFormField('foundingYear', e.target.value)}
        />
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Why this matters</h3>
        <p className="text-sm text-gray-600">
          These basic details will be prominently featured on your website and help establish credibility with your visitors.
          Your company name will be displayed in headers, while industry and founding year may be highlighted in the "About Us" section.
        </p>
      </div>
    </div>
  );
};

export default CompanyBasics;