import React, { useState, useEffect } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/Toaster';
import AdminLayout from '../../components/layout/AdminLayout';
import { 
  FileText, 
  Search, 
  ChevronDown, 
  MoreHorizontal, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  RefreshCw,
  Filter,
  Calendar,
  User,
  Edit,
  Eye,
  MessageSquare,
  Trash2
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  status: 'draft' | 'in_progress' | 'review' | 'completed';
  progress: number;
  created_at: string;
  updated_at: string;
  user_id: string;
  user_email: string;
  user_name: string;
  client_name: string;
}

const ProjectManagement: React.FC = () => {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<string>('updated_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  useEffect(() => {
    fetchProjects();
  }, [sortField, sortDirection, statusFilter]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      
      // Build the query
      let query = supabase
        .from('projects')
        .select(`
          *,
          users:user_id (
            email,
            first_name,
            last_name,
            client_name
          )
        `);
      
      // Apply status filter if not 'all'
      if (statusFilter !== 'all') {
        query = query.eq('status', statusFilter);
      }
      
      // Apply sorting
      const { data, error } = await query.order(sortField, { ascending: sortDirection === 'asc' });
      
      if (error) {
        throw error;
      }
      
      // Format the data
      const formattedProjects = data?.map(project => ({
        id: project.id,
        name: project.name,
        status: project.status,
        progress: project.progress,
        created_at: project.created_at,
        updated_at: project.updated_at,
        user_id: project.user_id,
        user_email: project.users?.email || 'Unknown',
        user_name: project.users?.first_name && project.users?.last_name 
          ? `${project.users.first_name} ${project.users.last_name}`
          : 'Unknown',
        client_name: project.users?.client_name || ''
      })) || [];
      
      setProjects(formattedProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast('Failed to load projects', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const handleStatusChange = async (projectId: string, newStatus: 'draft' | 'in_progress' | 'review' | 'completed') => {
    try {
      const { error } = await supabase
        .from('projects')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);
      
      if (error) throw error;
      
      // Update local state
      setProjects(projects.map(project => 
        project.id === projectId 
          ? { ...project, status: newStatus, updated_at: new Date().toISOString() } 
          : project
      ));
      
      toast(`Project status updated to ${getStatusText(newStatus)}`, 'success');
      
      // Close modal if open
      if (selectedProject && selectedProject.id === projectId) {
        setSelectedProject({
          ...selectedProject,
          status: newStatus,
          updated_at: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error updating project status:', error);
      toast('Failed to update project status', 'error');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is applied in the filtered projects computation
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'draft': return 'Draft';
      case 'in_progress': return 'In Progress';
      case 'review': return 'In Review';
      case 'completed': return 'Completed';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <AlertTriangle className="h-4 w-4 text-warning" />;
      case 'in_progress':
        return <RefreshCw className="h-4 w-4 text-primary" />;
      case 'review':
        return <Clock className="h-4 w-4 text-secondary" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-success" />;
      default:
        return null;
    }
  };

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'draft':
        return 'bg-warning/10 text-warning';
      case 'in_progress':
        return 'bg-primary/10 text-primary';
      case 'review':
        return 'bg-secondary/10 text-secondary';
      case 'completed':
        return 'bg-success/10 text-success';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredProjects = projects.filter((project) => {
    if (!searchQuery.trim()) {
      return true; // Show all projects when search is empty
    }
    
    const searchLower = searchQuery.toLowerCase().trim();
    const name = project.name.toLowerCase();
    const email = project.user_email.toLowerCase();
    const userName = project.user_name.toLowerCase();
    const clientName = project.client_name.toLowerCase();
    
    return (
      name.includes(searchLower) ||
      email.includes(searchLower) ||
      userName.includes(searchLower) ||
      clientName.includes(searchLower)
    );
  });

  const getClientDisplayName = (project: Project): string => {
    if (project.client_name) {
      return project.client_name;
    }
    
    if (project.user_name && project.user_name !== 'Unknown') {
      return project.user_name;
    }
    
    return project.user_email;
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight text-gray-900">Project Management</h1>
            <p className="mt-1 text-gray-500">
              View and manage all website projects.
            </p>
          </div>
        </div>

        <div className="card space-y-4">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <form onSubmit={handleSearch} className="flex w-full max-w-lg items-center">
              <div className="relative w-full">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="input pl-10 w-full"
                  placeholder="Search projects by name or client..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <button type="submit" className="btn btn-outline ml-2">
                Search
              </button>
            </form>

            <div className="flex items-center space-x-2">
              <div className="flex items-center">
                <Filter className="mr-2 h-5 w-5 text-gray-400" />
                <select
                  className="input"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="all">All Statuses</option>
                  <option value="draft">Draft</option>
                  <option value="in_progress">In Progress</option>
                  <option value="review">In Review</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center py-12">
              <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <>
              {filteredProjects.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <FileText className="mb-4 h-12 w-12 text-gray-400" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">No projects found</h3>
                  <p className="text-gray-500">
                    {searchQuery.trim()
                      ? `No projects match your search "${searchQuery}"`
                      : statusFilter !== 'all'
                      ? `No projects with status "${getStatusText(statusFilter)}"`
                      : "There are no projects in the system."}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('name')}
                        >
                          <div className="flex items-center">
                            <span>Project Name</span>
                            {sortField === 'name' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                        >
                          <div className="flex items-center">
                            <span>Client</span>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('status')}
                        >
                          <div className="flex items-center">
                            <span>Status</span>
                            {sortField === 'status' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('progress')}
                        >
                          <div className="flex items-center">
                            <span>Progress</span>
                            {sortField === 'progress' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('created_at')}
                        >
                          <div className="flex items-center">
                            <span>Created</span>
                            {sortField === 'created_at' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('updated_at')}
                        >
                          <div className="flex items-center">
                            <span>Last Updated</span>
                            {sortField === 'updated_at' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {filteredProjects.map((project) => (
                        <tr
                          key={project.id}
                          className="hover:bg-gray-50 cursor-pointer"
                          onClick={() => setSelectedProject(project)}
                        >
                          <td className="px-6 py-4">
                            <div className="font-medium text-gray-900">{project.name}</div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <div className="h-8 w-8 flex-shrink-0 rounded-full bg-primary/10 flex items-center justify-center">
                                <User className="h-4 w-4 text-primary" />
                              </div>
                              <div className="ml-3">
                                <div className="text-sm font-medium text-gray-900">
                                  {getClientDisplayName(project)}
                                </div>
                                <div className="text-xs text-gray-500">{project.user_email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeClass(project.status)}`}>
                              {getStatusIcon(project.status)}
                              <span className="ml-1">{getStatusText(project.status)}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="w-full max-w-xs">
                              <div className="h-2 w-full rounded-full bg-gray-200">
                                <div
                                  className={`h-2 rounded-full ${
                                    project.progress === 100
                                      ? 'bg-success'
                                      : project.progress > 50
                                      ? 'bg-secondary'
                                      : 'bg-primary'
                                  }`}
                                  style={{ width: `${project.progress}%` }}
                                ></div>
                              </div>
                              <div className="mt-1 text-right text-xs text-gray-500">
                                {project.progress}%
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center text-sm text-gray-500">
                              <Calendar className="mr-2 h-4 w-4 text-gray-400" />
                              {formatDate(project.created_at)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(project.updated_at)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              className="text-gray-500 hover:text-gray-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedProject(project);
                              }}
                            >
                              <MoreHorizontal className="h-5 w-5" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}
        </div>

        {/* Project Details Modal */}
        {selectedProject && (
          <div className="fixed inset-0 z-50 overflow-y-auto" onClick={() => setSelectedProject(null)}>
            <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>

              <div
                className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 sm:mx-0 sm:h-10 sm:w-10">
                      <FileText className="h-6 w-6 text-primary" />
                    </div>
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">
                        {selectedProject.name}
                      </h3>
                      <div className="mt-2">
                        <div className="flex items-center mb-2">
                          <User className="mr-2 h-5 w-5 text-gray-400" />
                          <p className="text-sm text-gray-500">
                            Client: {getClientDisplayName(selectedProject)} ({selectedProject.user_email})
                          </p>
                        </div>
                        <div className="flex items-center mb-2">
                          <Calendar className="mr-2 h-5 w-5 text-gray-400" />
                          <p className="text-sm text-gray-500">Created: {formatDate(selectedProject.created_at)}</p>
                        </div>
                        <div className="flex items-center mb-2">
                          <Clock className="mr-2 h-5 w-5 text-gray-400" />
                          <p className="text-sm text-gray-500">Last Updated: {formatDate(selectedProject.updated_at)}</p>
                        </div>

                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Project Status</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <button
                              className={`flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ${
                                selectedProject.status === 'draft'
                                  ? 'bg-warning/10 text-warning border border-warning'
                                  : 'bg-gray-100 text-gray-700 hover:bg-warning/10 hover:text-warning'
                              }`}
                              onClick={() => handleStatusChange(selectedProject.id, 'draft')}
                            >
                              <AlertTriangle className="mr-1 h-4 w-4" />
                              Draft
                            </button>
                            <button
                              className={`flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ${
                                selectedProject.status === 'in_progress'
                                  ? 'bg-primary/10 text-primary border border-primary'
                                  : 'bg-gray-100 text-gray-700 hover:bg-primary/10 hover:text-primary'
                              }`}
                              onClick={() => handleStatusChange(selectedProject.id, 'in_progress')}
                            >
                              <RefreshCw className="mr-1 h-4 w-4" />
                              In Progress
                            </button>
                            <button
                              className={`flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ${
                                selectedProject.status === 'review'
                                  ? 'bg-secondary/10 text-secondary border border-secondary'
                                  : 'bg-gray-100 text-gray-700 hover:bg-secondary/10 hover:text-secondary'
                              }`}
                              onClick={() => handleStatusChange(selectedProject.id, 'review')}
                            >
                              <Clock className="mr-1 h-4 w-4" />
                              In Review
                            </button>
                            <button
                              className={`flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium ${
                                selectedProject.status === 'completed'
                                  ? 'bg-success/10 text-success border border-success'
                                  : 'bg-gray-100 text-gray-700 hover:bg-success/10 hover:text-success'
                              }`}
                              onClick={() => handleStatusChange(selectedProject.id, 'completed')}
                            >
                              <CheckCircle className="mr-1 h-4 w-4" />
                              Completed
                            </button>
                          </div>
                        </div>

                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Project Progress</h4>
                          <div className="w-full">
                            <div className="h-2 w-full rounded-full bg-gray-200">
                              <div
                                className={`h-2 rounded-full ${
                                  selectedProject.progress === 100
                                    ? 'bg-success'
                                    : selectedProject.progress > 50
                                    ? 'bg-secondary'
                                    : 'bg-primary'
                                }`}
                                style={{ width: `${selectedProject.progress}%` }}
                              ></div>
                            </div>
                            <div className="mt-1 text-right text-xs text-gray-500">
                              {selectedProject.progress}% Complete
                            </div>
                          </div>
                        </div>

                        <div className="mt-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Actions</h4>
                          <div className="grid grid-cols-2 gap-2">
                            <button
                              className="flex items-center justify-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
                              onClick={() => toast('View project details coming soon!', 'info')}
                            >
                              <Eye className="mr-1 h-4 w-4" />
                              View Details
                            </button>
                            <button
                              className="flex items-center justify-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
                              onClick={() => toast('Edit project coming soon!', 'info')}
                            >
                              <Edit className="mr-1 h-4 w-4" />
                              Edit Project
                            </button>
                            <button
                              className="flex items-center justify-center rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200"
                              onClick={() => toast('Messaging coming soon!', 'info')}
                            >
                              <MessageSquare className="mr-1 h-4 w-4" />
                              Message Client
                            </button>
                            <button
                              className="flex items-center justify-center rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-100"
                              onClick={() => toast('Delete project coming soon!', 'info')}
                            >
                              <Trash2 className="mr-1 h-4 w-4" />
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedProject(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default ProjectManagement;
