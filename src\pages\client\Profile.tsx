import React, { useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useToast, ToastProvider } from '../../components/ui/Toaster';
import ClientLayout from '../../components/layout/ClientLayout';
import ProfileForm from '../../components/profile/ProfileForm';
import { Shield, Info } from 'lucide-react';

const ProfileContent: React.FC = () => {
  const { user, profile, updateProfile, refreshProfile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    // Refresh profile data when component mounts
    refreshProfile();
  }, [refreshProfile]);

  const handleProfileUpdate = async (data: {
    first_name: string;
    last_name: string;
    client_name?: string;
    avatar_url: string;
  }) => {
    try {
      const { error } = await updateProfile(data);
      
      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  if (!user || !profile) {
    return (
      <ClientLayout>
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="mx-auto mb-4 h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="text-gray-500">Loading profile information...</p>
          </div>
        </div>
      </ClientLayout>
    );
  }

  return (
    <ClientLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Profile Settings</h1>
          <p className="mt-1 text-gray-500">
            Manage your account information and preferences
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <ProfileForm
              userId={user.id}
              email={profile.email}
              firstName={profile.first_name}
              lastName={profile.last_name}
              clientName={profile.client_name}
              avatarUrl={profile.avatar_url}
              onProfileUpdate={handleProfileUpdate}
            />
          </div>

          <div className="space-y-6">
            <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
              <div className="mb-4 flex items-center text-primary">
                <Shield className="mr-2 h-5 w-5" />
                <h3 className="font-semibold">Account Security</h3>
              </div>
              <p className="mb-4 text-sm text-gray-600">
                Your account security is important to us. We recommend using a strong, unique password.
              </p>
              <button
                className="btn btn-outline w-full"
                onClick={() => toast('Password reset functionality coming soon!', 'info')}
              >
                Change Password
              </button>
            </div>

            <div className="rounded-xl border border-blue-100 bg-blue-50 p-6">
              <div className="mb-2 flex items-center text-blue-600">
                <Info className="mr-2 h-5 w-5" />
                <h3 className="font-semibold">Profile Tips</h3>
              </div>
              <ul className="space-y-2 text-sm text-blue-700">
                <li>• Adding a profile picture helps personalize your account</li>
                <li>• Your company name will be displayed on all your projects</li>
                <li>• Keep your contact information up to date for better communication</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </ClientLayout>
  );
};

const ClientProfile: React.FC = () => {
  return (
    <ToastProvider>
      <ProfileContent />
    </ToastProvider>
  );
};

export default ClientProfile;
