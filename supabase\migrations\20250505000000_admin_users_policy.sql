/*
  # Add Admin Policy for Users Table

  1. Security Changes
    - Add RLS policy for admin users to read all users
    - This allows admin users to view and manage client accounts
*/

-- Add policy for admins to read all users
CREATE POLICY "Admins can read all users"
  ON users
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
    )
  );
