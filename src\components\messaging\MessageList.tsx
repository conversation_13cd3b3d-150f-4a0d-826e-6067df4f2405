import React, { useRef, useEffect } from 'react';
import { Message } from '../../contexts/MessagingContext';
import { useAuth } from '../../contexts/AuthContext';
import { useMessaging } from '../../contexts/MessagingContext';
import { formatDistanceToNow } from 'date-fns';
import MessageStatus from './MessageStatus';
import MessageReaction from './MessageReaction';

interface MessageListProps {
  messages: Message[];
  loading: boolean;
}

const MessageList: React.FC<MessageListProps> = ({ messages, loading }) => {
  const { user } = useAuth();
  const { markAsRead } = useMessaging();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mark unread messages as read when they are displayed
  useEffect(() => {
    if (messages.length > 0 && user) {
      // Find messages that are unread and where the user is the receiver
      const unreadMessageIds = messages
        .filter(msg => !msg.read && msg.receiver_id === user.id)
        .map(msg => msg.id);

      // Mark these messages as read
      if (unreadMessageIds.length > 0) {
        markAsRead(unreadMessageIds);
      }
    }
  }, [messages, user, markAsRead]);

  if (loading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex h-96 flex-col items-center justify-center text-center">
        <div className="rounded-full bg-gray-100 p-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-10 w-10 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900">No messages yet</h3>
        <p className="mt-1 text-gray-500">Start the conversation by sending a message below.</p>
      </div>
    );
  }

  return (
    <div className="h-96 overflow-y-auto p-4">
      <div className="space-y-4">
        {messages.map((message) => {
          const isCurrentUser = message.sender_id === user?.id;
          const timeAgo = formatDistanceToNow(new Date(message.created_at), { addSuffix: true });

          return (
            <div
              key={message.id}
              className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-4`}
            >
              <div className="relative group">
                {/* Message bubble */}
                <div
                  className={`max-w-[75%] rounded-xl px-4 py-3 ${
                    isCurrentUser
                      ? 'bg-gray-100 text-gray-800 border border-primary'
                      : 'bg-primary text-white border border-black'
                  }`}
                >
                  {!isCurrentUser && (
                    <div className="mb-1 text-xs font-medium text-gray-500">
                      {message.sender_name || message.sender_email}
                    </div>
                  )}
                  <div className="text-sm">{message.content}</div>
                  <div
                    className={`mt-1 text-right text-xs flex items-center justify-end ${
                      isCurrentUser ? 'text-gray-500' : 'text-primary-100'
                    }`}
                  >
                    {isCurrentUser && <MessageStatus read={message.read} />}
                    <span className="ml-1">{timeAgo}</span>
                  </div>
                </div>

                {/* Reactions */}
                <MessageReaction message={message} isCurrentUser={isCurrentUser} />
              </div>
            </div>
          );
        })}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default MessageList;
