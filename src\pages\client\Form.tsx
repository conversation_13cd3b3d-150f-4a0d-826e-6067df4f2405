import React, { useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useAuth } from '../../contexts/AuthContext';
import { useToast, ToastProvider } from '../../components/ui/Toaster';
import ClientLayout from '../../components/layout/ClientLayout';
import { useFormStore, FormStep } from '../../stores/formStore';
import {
  Building2,
  Palette,
  Phone,
  Briefcase,
  ShoppingBag,
  Image,
  Users,
  MousePointer,
  LayoutList,
  ArrowLeft,
  ArrowRight,
  Save,
  Check
} from 'lucide-react';

// Form Steps Components
import CompanyBasics from './form-steps/CompanyBasics';
import BrandAssets from './form-steps/BrandAssets';
import ContactInfo from './form-steps/ContactInfo';
import BusinessDetails from './form-steps/BusinessDetails';
import ServicesProducts from './form-steps/ServicesProducts';
import VisualPreferences from './form-steps/VisualPreferences';
import TargetAudience from './form-steps/TargetAudience';
import CallToAction from './form-steps/CallToAction';
import ContentPriority from './form-steps/ContentPriority';

const FormContent: React.FC = () => {
  const { supabase } = useSupabase();
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { step } = useParams<{ step?: string }>();
  const [saving, setSaving] = useState(false);
  const [initialized, setInitialized] = useState(false);

  const {
    currentStep,
    setCurrentStep,
    projectId,
    setProjectId,
    formData,
    reset
  } = useFormStore();

  // Define the steps in order
  const steps: FormStep[] = [
    'company-basics',
    'brand-assets',
    'contact-info',
    'business-details',
    'services-products',
    'visual-preferences',
    'target-audience',
    'call-to-action',
    'content-priority',
  ];

  // Map steps to their display names and icons
  const stepInfo = {
    'company-basics': {
      name: 'Company Basics',
      icon: <Building2 className="h-5 w-5" />
    },
    'brand-assets': {
      name: 'Brand Assets',
      icon: <Palette className="h-5 w-5" />
    },
    'contact-info': {
      name: 'Contact Info',
      icon: <Phone className="h-5 w-5" />
    },
    'business-details': {
      name: 'Business Details',
      icon: <Briefcase className="h-5 w-5" />
    },
    'services-products': {
      name: 'Services/Products',
      icon: <ShoppingBag className="h-5 w-5" />
    },
    'visual-preferences': {
      name: 'Visual Preferences',
      icon: <Image className="h-5 w-5" />
    },
    'target-audience': {
      name: 'Target Audience',
      icon: <Users className="h-5 w-5" />
    },
    'call-to-action': {
      name: 'Call to Action',
      icon: <MousePointer className="h-5 w-5" />
    },
    'content-priority': {
      name: 'Content Priority',
      icon: <LayoutList className="h-5 w-5" />
    },
  };

  // Get current step index
  const currentIndex = steps.indexOf(currentStep);

  // On first load, check for projectId in URL
  useEffect(() => {
    const projectIdFromUrl = searchParams.get('project');

    if (projectIdFromUrl && !initialized) {
      setProjectId(projectIdFromUrl);
      loadProjectData(projectIdFromUrl);
    }

    // If step is provided in URL, set it as current step
    if (step && steps.includes(step as FormStep)) {
      setCurrentStep(step as FormStep);
    }

    setInitialized(true);
  }, [step, searchParams]);

  // Load project data from database
  const loadProjectData = async (id: string) => {
    if (!user) return;

    try {
      // First, get the project to verify ownership
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .eq('user_id', user.id)
        .single();

      if (projectError) throw projectError;
      if (!project) {
        toast('Project not found', 'error');
        navigate('/client/dashboard');
        return;
      }

      // Then, load all form submissions for this project
      const { data: submissions, error: submissionsError } = await supabase
        .from('form_submissions')
        .select('*')
        .eq('project_id', id);

      if (submissionsError) throw submissionsError;

      if (submissions && submissions.length > 0) {
        // Combine all submissions into a single form data object
        const combinedData = { ...formData };

        submissions.forEach(submission => {
          const stepData = submission.data;
          Object.assign(combinedData, stepData);
        });

        // Reset the form and then set the combined data
        reset();
        Object.entries(combinedData).forEach(([key, value]) => {
          if (key in formData) {
            useFormStore.setState(state => ({
              formData: {
                ...state.formData,
                [key]: value
              }
            }));
          }
        });
      }

    } catch (error) {
      console.error('Error loading project data:', error);
      toast('Failed to load project data', 'error');
    }
  };

  // Save current step data to database
  const saveStep = async () => {
    if (!user) return;

    setSaving(true);

    try {
      let currentProjectId = projectId;

      // If no project exists yet, create one
      if (!currentProjectId) {
        const { data: newProject, error: projectError } = await supabase
          .from('projects')
          .insert({
            user_id: user.id,
            name: formData.companyName || 'New Website Project',
            status: 'draft',
            progress: Math.floor((currentIndex / steps.length) * 100)
          })
          .select()
          .single();

        if (projectError) throw projectError;

        currentProjectId = newProject.id;
        setProjectId(currentProjectId);
      } else {
        // Update project progress
        await supabase
          .from('projects')
          .update({
            name: formData.companyName || 'New Website Project',
            progress: Math.floor(((currentIndex + 1) / steps.length) * 100),
            updated_at: new Date().toISOString()
          })
          .eq('id', currentProjectId);
      }

      // Determine which data to save based on current step
      let stepData = {};

      switch (currentStep) {
        case 'company-basics':
          stepData = {
            companyName: formData.companyName,
            industry: formData.industry,
            foundingYear: formData.foundingYear
          };
          break;
        case 'brand-assets':
          stepData = {
            logo: formData.logo,
            colorPalette: formData.colorPalette,
            typography: formData.typography
          };
          break;
        case 'contact-info':
          stepData = {
            address: formData.address,
            phone: formData.phone,
            email: formData.email,
            socialMedia: formData.socialMedia
          };
          break;
        case 'business-details':
          stepData = {
            missionStatement: formData.missionStatement,
            valueProposition: formData.valueProposition
          };
          break;
        case 'services-products':
          stepData = {
            offerings: formData.offerings
          };
          break;
        case 'visual-preferences':
          // Ensure arrays are properly initialized
          const inspirationUrls = Array.isArray(formData.inspirationUrls) ? formData.inspirationUrls : [];
          const competitorUrls = Array.isArray(formData.competitorUrls) ? formData.competitorUrls : [];

          stepData = {
            style: formData.style,
            inspirationUrls,
            competitorUrls
          };
          break;
        case 'target-audience':
          stepData = {
            primaryAudience: formData.primaryAudience,
            audienceAge: formData.audienceAge,
            audienceInterests: formData.audienceInterests
          };
          break;
        case 'call-to-action':
          stepData = {
            primaryCTA: formData.primaryCTA,
            secondaryCTA: formData.secondaryCTA
          };
          break;
        case 'content-priority':
          stepData = {
            contentSections: formData.contentSections
          };
          break;
      }

      try {
        // Check if a submission for this step already exists
        const { data: existingSubmission, error: queryError } = await supabase
          .from('form_submissions')
          .select('id')
          .eq('project_id', currentProjectId)
          .eq('step', currentStep)
          .maybeSingle();

        if (queryError) {
          console.error('Error checking for existing submission:', queryError);
          throw queryError;
        }

        if (existingSubmission) {
          // Update existing submission
          const { error: updateError } = await supabase
            .from('form_submissions')
            .update({
              data: stepData,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingSubmission.id);

          if (updateError) {
            throw updateError;
          }
        } else {
          // Create new submission
          const { error: insertError } = await supabase
            .from('form_submissions')
            .insert({
              project_id: currentProjectId,
              step: currentStep,
              data: stepData
            });

          if (insertError) {
            throw insertError;
          }
        }
      } catch (submissionError) {
        throw submissionError;
      }

      // If this is the last step, update project status
      if (currentIndex === steps.length - 1) {
        await supabase
          .from('projects')
          .update({
            status: 'review',
            progress: 100
          })
          .eq('id', currentProjectId);

        toast('Your website information has been submitted for review!', 'success');
        // Redirect to dashboard after completing all steps
        setTimeout(() => navigate('/client/dashboard'), 1500);
      } else {
        toast('Progress saved', 'success');
      }

    } catch (error) {
      toast('Failed to save your progress', 'error');
    } finally {
      setSaving(false);
    }
  };

  // Handle next step
  const handleNext = async () => {
    await saveStep();

    if (currentIndex < steps.length - 1) {
      const nextStep = steps[currentIndex + 1];
      setCurrentStep(nextStep);
      navigate(`/client/form/${nextStep}`);
    }
  };

  // Handle previous step
  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevStep = steps[currentIndex - 1];
      setCurrentStep(prevStep);
      navigate(`/client/form/${prevStep}`);
    }
  };

  // Render the appropriate step component
  const renderStepComponent = () => {
    switch (currentStep) {
      case 'company-basics':
        return <CompanyBasics />;
      case 'brand-assets':
        return <BrandAssets />;
      case 'contact-info':
        return <ContactInfo />;
      case 'business-details':
        return <BusinessDetails />;
      case 'services-products':
        return <ServicesProducts />;
      case 'visual-preferences':
        return <VisualPreferences />;
      case 'target-audience':
        return <TargetAudience />;
      case 'call-to-action':
        return <CallToAction />;
      case 'content-priority':
        return <ContentPriority />;
      default:
        return <CompanyBasics />;
    }
  };

  return (
    <ClientLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Website Information Form</h1>
          <p className="mt-1 text-gray-500">
            Complete the following steps to provide the information needed for your website.
          </p>
        </div>

        {/* Progress bar */}
        <div className="mb-8">
          <div className="mb-2 flex justify-between">
            <span className="text-sm font-medium text-gray-700">
              Step {currentIndex + 1} of {steps.length}
            </span>
            <span className="text-sm font-medium text-gray-700">
              {Math.floor(((currentIndex + 1) / steps.length) * 100)}% Complete
            </span>
          </div>
          <div className="h-2 w-full rounded-full bg-gray-200">
            <div
              className="h-2 rounded-full bg-primary transition-all duration-300"
              style={{ width: `${((currentIndex + 1) / steps.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Step indicator */}
        <div className="relative mb-8 hidden md:block">
          <div className="absolute left-0 right-0 top-4 h-0.5 bg-gray-200"></div>
          <div className="relative flex justify-between">
            {steps.map((stepKey, index) => {
              const isCompleted = index < currentIndex;
              const isActive = index === currentIndex;
              const stepDetails = stepInfo[stepKey];

              return (
                <div key={stepKey} className="flex flex-col items-center">
                  <div
                    className={`relative flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium ${
                      isCompleted
                        ? 'border-success bg-success text-white'
                        : isActive
                          ? 'border-primary bg-primary text-white'
                          : 'border-gray-300 bg-white text-gray-500'
                    }`}
                  >
                    {isCompleted ? <Check className="h-4 w-4" /> : index + 1}
                  </div>
                  <span className="mt-2 hidden text-xs lg:block">
                    {stepDetails.name}
                  </span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Step title */}
        <div className="mb-6 flex items-center">
          <div className="mr-3 rounded-full bg-primary/10 p-2 text-primary">
            {stepInfo[currentStep].icon}
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            {stepInfo[currentStep].name}
          </h2>
        </div>

        {/* Step content */}
        <div className="card animate-fade-in">
          {renderStepComponent()}
        </div>

        {/* Navigation buttons */}
        <div className="flex justify-between pt-4">
          <button
            type="button"
            onClick={handlePrevious}
            disabled={currentIndex === 0}
            className={`btn ${
              currentIndex === 0 ? 'btn-outline opacity-50' : 'btn-outline'
            }`}
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Previous
          </button>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={saveStep}
              className="btn btn-outline flex items-center"
              disabled={saving}
            >
              {saving ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-600 border-t-transparent"></div>
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              Save Progress
            </button>

            <button
              type="button"
              onClick={handleNext}
              className="btn btn-primary flex items-center"
              disabled={saving}
            >
              {currentIndex === steps.length - 1 ? 'Submit' : 'Next'}
              {currentIndex === steps.length - 1 ?
                <Check className="ml-2 h-4 w-4" /> :
                <ArrowRight className="ml-2 h-4 w-4" />
              }
            </button>
          </div>
        </div>
      </div>
    </ClientLayout>
  );
};

const ClientForm: React.FC = () => {
  return (
    <ToastProvider>
      <FormContent />
    </ToastProvider>
  );
};

export default ClientForm;