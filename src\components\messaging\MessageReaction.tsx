import React, { useState } from 'react';
import { Smile } from 'lucide-react';
import { Message, useMessaging } from '../../contexts/MessagingContext';
import { useAuth } from '../../contexts/AuthContext';
import ReactionSelector from './ReactionSelector';

interface MessageReactionProps {
  message: Message;
  isCurrentUser: boolean;
}

const MessageReaction: React.FC<MessageReactionProps> = ({ message, isCurrentUser }) => {
  const { user } = useAuth();
  const { addReaction, removeReaction } = useMessaging();
  const [showSelector, setShowSelector] = useState(false);

  // Only show reaction button on received messages, not on messages the user sends
  const shouldShowReactionButton = !isCurrentUser;

  // Get all reactions for this message
  const reactions = message.reactions || {};

  // Count total reactions
  const totalReactions = Object.values(reactions).reduce(
    (total, reaction) => total + reaction.count,
    0
  );

  // <PERSON>le adding a reaction
  const handleAddReaction = (emoji: string) => {
    addReaction(message.id, emoji);
    setShowSelector(false);
  };

  // Handle removing a reaction
  const handleRemoveReaction = (emoji: string) => {
    removeReaction(message.id, emoji);
  };

  // Check if the current user has reacted with a specific emoji
  const hasUserReacted = (emoji: string) => {
    if (!user || !reactions[emoji] || !reactions[emoji].users) return false;
    return !!reactions[emoji].users[user.id];
  };

  return (
    <div className="relative">
      {/* Reaction button - only visible on hover */}
      {shouldShowReactionButton && (
        <button
          onClick={() => setShowSelector(!showSelector)}
          className="absolute -left-8 top-1/2 -translate-y-1/2 rounded-full p-1 text-gray-400 opacity-0 transition-opacity hover:bg-gray-100 hover:text-primary group-hover:opacity-100"
        >
          <Smile className="h-5 w-5" />
        </button>
      )}

      {/* Reaction selector */}
      {showSelector && (
        <ReactionSelector
          onSelectReaction={handleAddReaction}
          onClose={() => setShowSelector(false)}
        />
      )}

      {/* Display reactions */}
      {totalReactions > 0 && (
        <div className="mt-1 flex flex-wrap gap-1">
          {Object.entries(reactions).map(([emoji, reaction]) => (
            <button
              key={emoji}
              onClick={() => hasUserReacted(emoji) ? handleRemoveReaction(emoji) : handleAddReaction(emoji)}
              className={`rounded-full px-2 py-0.5 text-xs ${
                hasUserReacted(emoji) ? 'bg-primary/10 text-primary' : 'bg-gray-100 text-gray-600'
              } hover:bg-gray-200`}
              title={reaction.users ? Object.values(reaction.users).map(u => u.name).join(', ') : ''}
            >
              {emoji} {reaction.count > 1 ? reaction.count : ''}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default MessageReaction;
