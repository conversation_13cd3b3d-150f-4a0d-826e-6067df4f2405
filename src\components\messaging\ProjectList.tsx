import React from 'react';
import { Project, useMessaging } from '../../contexts/MessagingContext';
import { useAuth } from '../../contexts/AuthContext';
import { MessageSquare, User } from 'lucide-react';

interface ProjectListProps {
  projects: Project[];
  activeProjectId: string | null;
  onSelectProject: (projectId: string) => void;
  loading: boolean;
}

const ProjectList: React.FC<ProjectListProps> = ({
  projects,
  activeProjectId,
  onSelectProject,
  loading
}) => {
  const { isAdmin } = useAuth();
  const { markProjectMessagesAsRead } = useMessaging();

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center p-4">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-4 text-center">
        <MessageSquare className="h-10 w-10 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
        <p className="mt-1 text-xs text-gray-500">
          {isAdmin()
            ? "There are no projects available for messaging."
            : "Create a website project to start messaging with our team."}
        </p>
      </div>
    );
  }



  return (
    <div className="h-full overflow-y-auto">
      <div className="space-y-1 p-2">
        {projects.map((project) => {
          const isActive = project.id === activeProjectId;
          const displayName = isAdmin()
            ? project.client_name || project.user_name || project.user_email || 'Unknown Client'
            : project.name;

          return (
            <button
              key={project.id}
              className={`flex w-full items-center justify-between rounded-xl px-3 py-2 text-left transition-colors ${
                isActive
                  ? 'bg-primary/10 text-primary'
                  : project.unread_count > 0
                    ? 'bg-error/10 text-gray-900 hover:bg-error/20 hover:text-error border-l-4 border-error font-medium shadow-sm'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
              }`}
              onClick={() => {
                // First select the project
                onSelectProject(project.id);

                // Then mark messages as read when selecting a project
                if (project.unread_count > 0) {
                  markProjectMessagesAsRead(project.id);
                }
              }}
            >
              <div className="flex items-center">
                <div className={`mr-3 flex h-8 w-8 items-center justify-center rounded-full ${
                  isActive ? 'bg-primary/20' : 'bg-gray-100'
                }`}>
                  {isAdmin() ? (
                    <User className={`h-4 w-4 ${isActive ? 'text-primary' : 'text-gray-500'}`} />
                  ) : (
                    <MessageSquare className={`h-4 w-4 ${isActive ? 'text-primary' : 'text-gray-500'}`} />
                  )}
                </div>
                <div>
                  <div className="font-medium">{displayName}</div>
                  <div className="text-xs text-gray-500">
                    {isAdmin() ? project.name : 'Support Chat'}
                  </div>
                </div>
              </div>
              {project.unread_count > 0 && (
                <div className="flex h-6 min-w-6 items-center justify-center rounded-full bg-error px-2 text-xs font-bold text-white animate-pulse-subtle shadow-md">
                  {project.unread_count}
                </div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default ProjectList;
