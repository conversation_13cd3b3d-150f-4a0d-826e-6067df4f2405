/*
  # Add Receiver ID to Messages Table

  1. Schema Changes
    - Add receiver_id column to messages table
    - This column will store the ID of the message recipient
    - Used for tracking read status and message direction
*/

-- Add receiver_id column to messages table if it doesn't exist
ALTER TABLE messages ADD COLUMN IF NOT EXISTS receiver_id UUID REFERENCES users(id);

-- Create index for receiver_id column
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON messages(receiver_id);

-- Update RLS policies to include receiver_id in conditions
CREATE OR REPLACE POLICY "Users can view messages for their projects"
  ON messages
  FOR SELECT
  USING (
    (auth.uid() IN (SELECT projects.user_id FROM projects WHERE projects.id = messages.project_id)) OR
    (EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'))
  );

CREATE OR REPLACE POLICY "Users can insert messages for their projects"
  ON messages
  FOR INSERT
  WITH CHECK (
    (sender_id = auth.uid()) AND
    (
      (auth.uid() IN (SELECT projects.user_id FROM projects WHERE projects.id = messages.project_id)) OR
      (EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'))
    )
  );

CREATE OR REPLACE POLICY "Users can update their own messages"
  ON messages
  FOR UPDATE
  USING (
    (sender_id = auth.uid()) OR
    (EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'))
  );
