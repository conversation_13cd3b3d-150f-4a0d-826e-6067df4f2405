import React, { useEffect, useState } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/Toaster';
import AdminLayout from '../../components/layout/AdminLayout';
import {
  FileCode,
  Copy,
  Check,
  RotateCw,
  Download,
  Code,
  Settings,
  ArrowRight,
  Sparkles,
  Save
} from 'lucide-react';

interface Project {
  id: string;
  name: string;
  user_email: string;
  status: string;
  created_at: string;
}

interface FormData {
  [key: string]: any;
}

const PromptGenerator: React.FC = () => {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [formData, setFormData] = useState<FormData | null>(null);
  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [copied, setCopied] = useState(false);
  const [promptSaved, setPromptSaved] = useState(false);

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setLoading(true);

      // Get projects that are in review status
      const { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          status,
          created_at,
          users!projects_user_id_fkey (
            email
          )
        `)
        .eq('status', 'review')
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedProjects = (data || []).map(project => ({
        id: project.id,
        name: project.name,
        user_email: project.users?.email || 'Unknown',
        status: project.status,
        created_at: project.created_at
      }));

      setProjects(formattedProjects);
    } catch (error) {
      toast('Failed to load projects', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjectData = async (projectId: string) => {
    try {
      setLoading(true);
      setFormData(null);
      setGeneratedPrompt('');
      setPromptSaved(false);

      // Get all form submissions for the project
      const { data, error } = await supabase
        .from('form_submissions')
        .select('*')
        .eq('project_id', projectId);

      if (error) throw error;

      // Combine all submission data
      const combinedData: FormData = {};
      data?.forEach(submission => {
        Object.assign(combinedData, submission.data);
      });

      setFormData(combinedData);

      // Check if prompt already exists
      const { data: promptData } = await supabase
        .from('prompts')
        .select('content')
        .eq('project_id', projectId)
        .maybeSingle();

      if (promptData?.content) {
        setGeneratedPrompt(promptData.content);
        setPromptSaved(true);
      }

    } catch (error) {
      toast('Failed to load project data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const generatePrompt = () => {
    if (!formData) return;

    setGenerating(true);

    setTimeout(() => {
      try {
        // Generate the prompt based on the form data
        const prompt = createPrompt(formData);
        setGeneratedPrompt(prompt);
        setPromptSaved(false);
      } catch (error) {
        toast('Failed to generate prompt', 'error');
      } finally {
        setGenerating(false);
      }
    }, 1000); // Simulated delay for generation
  };

  const createPrompt = (data: FormData): string => {
    // Structured prompt template
    let prompt = `Create a professional one-page website for ${data.companyName || 'my company'}, a ${data.industry || 'business'} that was established in ${data.foundingYear || 'recently'}.

The website should include the following sections in this order:
`;

    // Add sections based on content priority
    if (data.contentSections && data.contentSections.length > 0) {
      data.contentSections.forEach((section: string, index: number) => {
        prompt += `${index + 1}. ${section}\n`;
      });
    } else {
      prompt += `1. Hero/Introduction
2. About Us
3. Services/Products
4. Testimonials
5. Contact
`;
    }

    // Add business description
    prompt += `\nBusiness Details:
- Mission Statement: ${data.missionStatement || 'Not provided'}
- Unique Value Proposition: ${data.valueProposition || 'Not provided'}
`;

    // Add services/products
    if (data.offerings && data.offerings.length > 0) {
      prompt += `\nServices/Products:\n`;
      data.offerings.forEach((offering: any, index: number) => {
        prompt += `- ${offering.name || `Service ${index + 1}`}: ${offering.description || 'No description'} ${
          offering.price ? `(Price: ${offering.price})` : ''
        }\n`;

        if (offering.features && offering.features.length > 0) {
          prompt += `  Features:\n`;
          offering.features.forEach((feature: string) => {
            prompt += `  • ${feature}\n`;
          });
        }
      });
    }

    // Add visual style preferences
    prompt += `\nDesign Preferences:
- Style: ${data.style || 'Modern and professional'}
- Color Palette: ${data.colorPalette && data.colorPalette.length > 0 ? data.colorPalette.join(', ') : 'Use appropriate colors for the industry'}
- Typography: Headings - ${data.typography?.headingFont || 'Sans-serif'}, Body - ${data.typography?.bodyFont || 'Sans-serif'}
`;

    // Add target audience info
    if (data.primaryAudience) {
      prompt += `\nTarget Audience: ${data.primaryAudience}`;
      if (data.audienceAge) {
        prompt += ` (Age range: ${data.audienceAge})`;
      }
      prompt += '\n';

      if (data.audienceInterests && data.audienceInterests.length > 0) {
        prompt += `Audience Interests: ${data.audienceInterests.join(', ')}\n`;
      }
    }

    // Add call to action
    if (data.primaryCTA) {
      prompt += `\nPrimary Call-to-Action: "${data.primaryCTA}"`;
      if (data.secondaryCTA) {
        prompt += `\nSecondary Call-to-Action: "${data.secondaryCTA}"`;
      }
      prompt += '\n';
    }

    // Add contact information
    prompt += `\nContact Information:
- Email: ${data.email || 'Not provided'}
- Phone: ${data.phone || 'Not provided'}
- Address: ${data.address || 'Not provided'}
`;

    // Add social media
    if (data.socialMedia) {
      const socialLinks = Object.entries(data.socialMedia)
        .filter(([_, value]) => value)
        .map(([key, value]) => `${key}: ${value}`);

      if (socialLinks.length > 0) {
        prompt += `- Social Media: ${socialLinks.join(', ')}\n`;
      }
    }

    // Add technical requirements
    prompt += `\nTechnical Requirements:
- Responsive design that works well on mobile, tablet, and desktop
- Fast loading times
- SEO optimization
- Accessibility compliance
- Contact form functionality
`;

    // Add design inspiration notes
    if (data.inspirationUrls && data.inspirationUrls.length > 0) {
      prompt += `\nDesign Inspiration: The website should draw inspiration from sites like ${data.inspirationUrls.join(', ')}\n`;
    }

    // Add final notes
    prompt += `\nCreate this website using modern web technologies. Make it visually appealing, user-friendly, and aligned with ${data.companyName || 'the company'}'s brand identity. Ensure all text is professionally written and error-free.`;

    return prompt;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedPrompt);
    setCopied(true);
    toast('Prompt copied to clipboard', 'success');

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const downloadPrompt = () => {
    const blob = new Blob([generatedPrompt], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `prompt-${selectedProject}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast('Prompt downloaded', 'success');
  };

  const savePrompt = async () => {
    if (!selectedProject || !generatedPrompt) return;

    try {
      // Check if prompt already exists
      const { data: existingPrompt } = await supabase
        .from('prompts')
        .select('id')
        .eq('project_id', selectedProject)
        .maybeSingle();

      if (existingPrompt) {
        // Update existing prompt
        await supabase
          .from('prompts')
          .update({
            content: generatedPrompt,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingPrompt.id);
      } else {
        // Create new prompt
        await supabase
          .from('prompts')
          .insert({
            project_id: selectedProject,
            content: generatedPrompt
          });
      }

      setPromptSaved(true);
      toast('Prompt saved successfully', 'success');
    } catch (error) {
      toast('Failed to save prompt', 'error');
    }
  };

  const openInAI = () => {
    // Open bolt.new in a new tab with the prompt
    const encodedPrompt = encodeURIComponent(generatedPrompt);
    window.open(`https://bolt.new?prompt=${encodedPrompt}`, '_blank');
  };

  const updateProjectStatus = async (status: 'completed' | 'in_progress') => {
    if (!selectedProject) return;

    try {
      await supabase
        .from('projects')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedProject);

      // Refresh the projects list
      fetchProjects();

      toast(`Project marked as ${status.replace('_', ' ')}`, 'success');

      if (status === 'completed') {
        setSelectedProject('');
        setFormData(null);
        setGeneratedPrompt('');
      }
    } catch (error) {
      toast('Failed to update project status', 'error');
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Prompt Generator</h1>
          <p className="mt-1 text-gray-500">
            Generate optimized prompts for bolt.new based on client submissions.
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          <div className="card lg:col-span-1">
            <h2 className="mb-4 text-lg font-medium">Projects Ready for Review</h2>

            {loading && !selectedProject ? (
              <div className="flex justify-center py-8">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
            ) : projects.length === 0 ? (
              <div className="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 py-12 text-center">
                <FileCode className="mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">No projects to review</h3>
                <p className="mb-4 text-gray-500">
                  When clients submit their website information, they'll appear here.
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-[600px] overflow-y-auto pr-2">
                {projects.map((project) => (
                  <div
                    key={project.id}
                    className={`rounded-lg border p-4 cursor-pointer transition-colors ${
                      selectedProject === project.id
                        ? 'border-primary bg-primary/5'
                        : 'border-gray-200 hover:border-primary/50 hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      setSelectedProject(project.id);
                      fetchProjectData(project.id);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{project.name}</h3>
                      <span className="inline-flex items-center rounded-full bg-secondary/10 px-2.5 py-0.5 text-xs font-medium text-secondary">
                        Review
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">
                      Client: {project.user_email}
                    </p>
                    <p className="text-xs text-gray-400">
                      Submitted: {new Date(project.created_at).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="card lg:col-span-2">
            {!selectedProject ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Code className="mb-4 h-16 w-16 text-primary/40" />
                <h3 className="mb-2 text-xl font-medium text-gray-900">Prompt Generator</h3>
                <p className="mb-6 max-w-md text-gray-500">
                  Select a project from the list to view client information and generate a prompt for bolt.new.
                </p>
              </div>
            ) : loading ? (
              <div className="flex justify-center py-12">
                <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <h2 className="text-xl font-medium">Generate Prompt</h2>
                  <div className="mt-2 flex space-x-2 sm:mt-0">
                    <button
                      className="btn btn-outline btn-sm flex items-center"
                      onClick={() => updateProjectStatus('in_progress')}
                    >
                      <RotateCw className="mr-2 h-4 w-4" /> Mark In Progress
                    </button>
                    <button
                      className="btn btn-success btn-sm flex items-center"
                      onClick={() => updateProjectStatus('completed')}
                    >
                      <Check className="mr-2 h-4 w-4" /> Mark Completed
                    </button>
                  </div>
                </div>

                {formData && (
                  <div className="rounded-lg bg-gray-50 p-4">
                    <div className="mb-4 flex items-center justify-between">
                      <h3 className="font-medium">Client Information Summary</h3>
                      <button
                        className="btn btn-primary btn-sm flex items-center"
                        onClick={generatePrompt}
                        disabled={generating}
                      >
                        {generating ? (
                          <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Sparkles className="mr-2 h-4 w-4" />
                        )}
                        Generate Prompt
                      </button>
                    </div>

                    <div className="space-y-3 max-h-[200px] overflow-y-auto pr-2">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Company Details:</p>
                          <ul className="list-inside list-disc text-sm text-gray-600">
                            <li>Name: {formData.companyName || 'Not provided'}</li>
                            <li>Industry: {formData.industry || 'Not provided'}</li>
                            <li>Founded: {formData.foundingYear || 'Not provided'}</li>
                          </ul>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700">Brand Assets:</p>
                          <ul className="list-inside list-disc text-sm text-gray-600">
                            <li>Logo: {formData.logo ? 'Provided' : 'Not provided'}</li>
                            <li>Colors: {formData.colorPalette?.length || 0} colors</li>
                            <li>
                              Typography: {formData.typography?.headingFont || 'Not specified'} /
                              {formData.typography?.bodyFont || 'Not specified'}
                            </li>
                          </ul>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700">Services/Products:</p>
                        <p className="text-sm text-gray-600">
                          {formData.offerings?.length || 0} offerings provided
                        </p>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700">Content Sections Priority:</p>
                        <p className="text-sm text-gray-600">
                          {formData.contentSections?.length > 0
                            ? formData.contentSections.join(', ')
                            : 'Default order'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {generatedPrompt && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Generated Prompt</h3>
                      <div className="flex space-x-2">
                        <button
                          className="btn btn-outline btn-sm flex items-center"
                          onClick={downloadPrompt}
                        >
                          <Download className="mr-2 h-4 w-4" /> Download
                        </button>
                        <button
                          className="btn btn-outline btn-sm flex items-center"
                          onClick={copyToClipboard}
                        >
                          {copied ? (
                            <Check className="mr-2 h-4 w-4 text-success" />
                          ) : (
                            <Copy className="mr-2 h-4 w-4" />
                          )}
                          {copied ? 'Copied' : 'Copy'}
                        </button>
                        <button
                          className="btn btn-primary btn-sm flex items-center"
                          onClick={savePrompt}
                          disabled={promptSaved}
                        >
                          <Save className="mr-2 h-4 w-4" />
                          {promptSaved ? 'Saved' : 'Save'}
                        </button>
                      </div>
                    </div>

                    <div className="relative">
                      <pre className="max-h-[300px] overflow-y-auto rounded-md bg-gray-800 p-4 text-sm text-white">
                        {generatedPrompt}
                      </pre>
                      <div className="absolute bottom-4 right-4">
                        <button
                          className="btn btn-sm bg-primary text-white hover:bg-primary-dark flex items-center"
                          onClick={openInAI}
                        >
                          Open in bolt.new <ArrowRight className="ml-2 h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {!formData && !loading && (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <Settings className="mb-4 h-12 w-12 text-gray-400 animate-spin-slow" />
                    <h3 className="mb-2 text-lg font-medium text-gray-900">Loading project data</h3>
                    <p className="text-gray-500">
                      Retrieving client information...
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default PromptGenerator;