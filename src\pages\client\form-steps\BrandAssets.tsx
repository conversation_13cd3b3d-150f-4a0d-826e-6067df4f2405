import React, { useState } from 'react';
import { useFormStore } from '../../../stores/formStore';
import { Trash2 } from 'lucide-react';

const BrandAssets: React.FC = () => {
  const { formData, updateFormField, updateNestedFormField } = useFormStore();
  const [newColor, setNewColor] = useState('#000000');

  const addColor = () => {
    if (newColor && !formData.colorPalette.includes(newColor)) {
      updateFormField('colorPalette', [...formData.colorPalette, newColor]);
      setNewColor('#000000');
    }
  };

  const removeColor = (colorToRemove: string) => {
    updateFormField(
      'colorPalette',
      formData.colorPalette.filter(color => color !== colorToRemove)
    );
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Your brand's visual elements help establish recognition and consistency. Provide your logo and color preferences.
      </p>

      <div className="form-group">
        <label htmlFor="logo" className="form-label">
          Logo URL
        </label>
        <input
          type="text"
          id="logo"
          className="input w-full"
          placeholder="https://example.com/your-logo.png"
          value={formData.logo}
          onChange={(e) => updateFormField('logo', e.target.value)}
        />
        <p className="mt-1 text-xs text-gray-500">
          Provide a URL to your logo image. Ideally PNG format with transparent background.
        </p>
      </div>

      <div className="form-group">
        <label className="form-label">Color Palette</label>
        <div className="flex flex-wrap gap-2 mb-3">
          {formData.colorPalette.map((color, index) => (
            <div key={index} className="flex items-center">
              <div 
                className="h-8 w-8 rounded-md border border-gray-300" 
                style={{ backgroundColor: color }}
              ></div>
              <span className="mx-2 text-sm">{color}</span>
              <button
                type="button"
                className="p-1 text-gray-500 hover:text-error"
                onClick={() => removeColor(color)}
              >
                <Trash2 size={16} />
              </button>
            </div>
          ))}
          {formData.colorPalette.length === 0 && (
            <p className="text-sm text-gray-500 italic">No colors added yet</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="color"
            value={newColor}
            onChange={(e) => setNewColor(e.target.value)}
            className="h-8 w-10 cursor-pointer rounded border-0"
          />
          <input
            type="text"
            value={newColor}
            onChange={(e) => setNewColor(e.target.value)}
            className="input w-24"
          />
          <button
            type="button"
            onClick={addColor}
            className="btn btn-outline btn-sm"
          >
            Add Color
          </button>
        </div>
      </div>

      <div className="form-group">
        <label htmlFor="headingFont" className="form-label">
          Heading Font Preference
        </label>
        <select
          id="headingFont"
          className="input w-full"
          value={formData.typography.headingFont}
          onChange={(e) => updateNestedFormField('typography', 'headingFont', e.target.value)}
        >
          <option value="">Select a font</option>
          <option value="Arial">Arial (Sans-serif)</option>
          <option value="Helvetica">Helvetica (Sans-serif)</option>
          <option value="Georgia">Georgia (Serif)</option>
          <option value="Times New Roman">Times New Roman (Serif)</option>
          <option value="Verdana">Verdana (Sans-serif)</option>
          <option value="Roboto">Roboto (Sans-serif)</option>
          <option value="Open Sans">Open Sans (Sans-serif)</option>
          <option value="Lato">Lato (Sans-serif)</option>
          <option value="Montserrat">Montserrat (Sans-serif)</option>
          <option value="Playfair Display">Playfair Display (Serif)</option>
        </select>
      </div>

      <div className="form-group">
        <label htmlFor="bodyFont" className="form-label">
          Body Text Font Preference
        </label>
        <select
          id="bodyFont"
          className="input w-full"
          value={formData.typography.bodyFont}
          onChange={(e) => updateNestedFormField('typography', 'bodyFont', e.target.value)}
        >
          <option value="">Select a font</option>
          <option value="Arial">Arial (Sans-serif)</option>
          <option value="Helvetica">Helvetica (Sans-serif)</option>
          <option value="Georgia">Georgia (Serif)</option>
          <option value="Times New Roman">Times New Roman (Serif)</option>
          <option value="Verdana">Verdana (Sans-serif)</option>
          <option value="Roboto">Roboto (Sans-serif)</option>
          <option value="Open Sans">Open Sans (Sans-serif)</option>
          <option value="Lato">Lato (Sans-serif)</option>
          <option value="Source Sans Pro">Source Sans Pro (Sans-serif)</option>
          <option value="PT Sans">PT Sans (Sans-serif)</option>
        </select>
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Branding Tip</h3>
        <p className="text-sm text-gray-600">
          Choose colors that reflect your brand's personality. Use tools like Adobe Color or Coolors to create harmonious color palettes.
          For fonts, serif fonts (like Times New Roman) convey tradition and reliability, while sans-serif fonts (like Arial) feel modern and clean.
        </p>
      </div>
    </div>
  );
};

export default BrandAssets;