import React, { useEffect, useState } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/Toaster';
import AdminLayout from '../../components/layout/AdminLayout';
import {
  User,
  FileText,
  Mail,
  Calendar,
  Search,
  ChevronDown,
  MoreHorizontal,
  XCircle,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';

interface Client {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  client_name: string | null;
  created_at: string;
  projectCount: number;
  lastActivity: string | null;
}

const ClientManagement: React.FC = () => {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<string>('created_at');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  useEffect(() => {
    fetchClients();
  }, [sortField, sortDirection, supabase]);

  const fetchClients = async () => {
    try {
      setLoading(true);

      // First, check if we can access the users table at all
      const { count, error: userError } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      if (userError) {
        toast('Error accessing users table. This may be due to permissions.', 'error');
      }

      // Get all clients
      const { data: clientData, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'client')
        .order(sortField, { ascending: sortDirection === 'asc' });

      if (error) {
        toast(`Error fetching clients: ${error.message}`, 'error');
        throw error;
      }

      if (!clientData || clientData.length === 0) {
        setClients([]);
        setLoading(false);
        return;
      }

      // Process each client to get project counts and last activity
      const processedClients = [];

      for (const client of clientData) {
        try {
          // Get project count
          let projectCount = 0;
          try {
            const { count, error: countError } = await supabase
              .from('projects')
              .select('*', { count: 'exact', head: true })
              .eq('user_id', client.id);

            if (!countError) {
              projectCount = count || 0;
            }
          } catch (countError) {
            // Silent error handling
          }

          // Get last activity date
          let lastActivity = null;
          try {
            const { data: latestProject } = await supabase
              .from('projects')
              .select('updated_at')
              .eq('user_id', client.id)
              .order('updated_at', { ascending: false })
              .limit(1);

            if (latestProject && latestProject.length > 0) {
              lastActivity = latestProject[0].updated_at;
            }
          } catch (activityError) {
            // Silent error handling
          }

          processedClients.push({
            ...client,
            projectCount: projectCount,
            lastActivity: lastActivity,
          });
        } catch (clientError) {
          // Still add the client with default values
          processedClients.push({
            ...client,
            projectCount: 0,
            lastActivity: null,
          });
        }
      }

      setClients(processedClients);
    } catch (error) {
      toast('Failed to load clients', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // For client-side sorting fields
      if (field === 'projectCount' || field === 'lastActivity') {
        // Sort the clients array directly
        const sortedClients = [...clients].sort((a, b) => {
          if (field === 'projectCount') {
            return sortDirection === 'asc'
              ? a.projectCount - b.projectCount
              : b.projectCount - a.projectCount;
          } else if (field === 'lastActivity') {
            // Handle null values
            if (!a.lastActivity) return sortDirection === 'asc' ? -1 : 1;
            if (!b.lastActivity) return sortDirection === 'asc' ? 1 : -1;

            return sortDirection === 'asc'
              ? new Date(a.lastActivity).getTime() - new Date(b.lastActivity).getTime()
              : new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime();
          }
          return 0;
        });
        setClients(sortedClients);
      } else {
        // For server-side sorting fields
        setSortField(field);
        setSortDirection('asc');
      }
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Filter is applied in the filtered clients computation
  };

  const filteredClients = clients.filter((client) => {
    if (!searchQuery.trim()) {
      return true; // Show all clients when search is empty
    }

    const searchLower = searchQuery.toLowerCase().trim();
    const email = (client.email || '').toLowerCase();
    const firstName = (client.first_name || '').toLowerCase();
    const lastName = (client.last_name || '').toLowerCase();
    const clientName = (client.client_name || '').toLowerCase();
    const fullName = `${firstName} ${lastName}`.trim();

    return (
      email.includes(searchLower) ||
      firstName.includes(searchLower) ||
      lastName.includes(searchLower) ||
      fullName.includes(searchLower) ||
      clientName.includes(searchLower)
    );
  });

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight text-gray-900">Client Management</h1>
            <p className="mt-1 text-gray-500">
              View and manage all clients using the platform.
            </p>
          </div>
        </div>

        <div className="card space-y-4">
          <form onSubmit={handleSearch} className="flex w-full max-w-lg items-center">
            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="input pl-10 w-full"
                placeholder="Search clients by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <button type="submit" className="btn btn-outline ml-2">
              Search
            </button>
          </form>

          {loading ? (
            <div className="flex justify-center py-12">
              <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <>
              {filteredClients.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <XCircle className="mb-4 h-12 w-12 text-gray-400" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">No clients found</h3>
                  <p className="text-gray-500">
                    {searchQuery.trim()
                      ? `No clients match your search "${searchQuery}"`
                      : "There are no clients registered in the system."}
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('email')}
                        >
                          <div className="flex items-center">
                            <span>Client</span>
                            {sortField === 'email' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('projectCount')}
                        >
                          <div className="flex items-center">
                            <span>Projects</span>
                            {sortField === 'projectCount' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('created_at')}
                        >
                          <div className="flex items-center">
                            <span>Joined</span>
                            {sortField === 'created_at' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 cursor-pointer"
                          onClick={() => handleSort('lastActivity')}
                        >
                          <div className="flex items-center">
                            <span>Last Activity</span>
                            {sortField === 'lastActivity' && (
                              <ChevronDown
                                className={`ml-1 h-4 w-4 transform ${
                                  sortDirection === 'asc' ? 'rotate-180' : ''
                                }`}
                              />
                            )}
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                        >
                          Status
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {filteredClients.map((client) => (
                        <tr
                          key={client.id}
                          className="hover:bg-gray-50 cursor-pointer"
                          onClick={() => setSelectedClient(client)}
                        >
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <div className="h-10 w-10 flex-shrink-0 rounded-full bg-primary/10 flex items-center justify-center">
                                <User className="h-6 w-6 text-primary" />
                              </div>
                              <div className="ml-4">
                                <div className="font-medium text-gray-900">
                                  {client.client_name
                                    ? client.client_name
                                    : (client.first_name && client.last_name
                                      ? `${client.first_name} ${client.last_name}`
                                      : 'Unnamed Client')}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {client.client_name && client.first_name && client.last_name
                                    ? `${client.first_name} ${client.last_name} · `
                                    : ''}
                                  {client.email}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <FileText className="mr-2 h-5 w-5 text-gray-400" />
                              <span>{client.projectCount}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center text-sm text-gray-500">
                              <Calendar className="mr-2 h-5 w-5 text-gray-400" />
                              {formatDate(client.created_at)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {client.lastActivity ? formatDate(client.lastActivity) : 'Never'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {client.projectCount > 0 ? (
                              <span className="inline-flex items-center rounded-full bg-success/10 px-2.5 py-0.5 text-xs font-medium text-success">
                                <CheckCircle className="mr-1 h-3 w-3" /> Active
                              </span>
                            ) : (
                              <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                                <AlertTriangle className="mr-1 h-3 w-3" /> Inactive
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              className="text-gray-500 hover:text-gray-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                toast('Actions coming soon!', 'info');
                              }}
                            >
                              <MoreHorizontal className="h-5 w-5" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}
        </div>

        {/* Client Details Modal */}
        {selectedClient && (
          <div className="fixed inset-0 z-50 overflow-y-auto" onClick={() => setSelectedClient(null)}>
            <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">&#8203;</span>

              <div
                className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-primary/10 sm:mx-0 sm:h-10 sm:w-10">
                      <User className="h-6 w-6 text-primary" />
                    </div>
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                      <h3 className="text-lg font-medium leading-6 text-gray-900">
                        {selectedClient.client_name
                          ? selectedClient.client_name
                          : (selectedClient.first_name && selectedClient.last_name
                            ? `${selectedClient.first_name} ${selectedClient.last_name}`
                            : 'Unnamed Client')}
                      </h3>
                      {selectedClient.client_name && selectedClient.first_name && selectedClient.last_name && (
                        <p className="text-sm text-gray-600">
                          Contact: {selectedClient.first_name} {selectedClient.last_name}
                        </p>
                      )}
                      <div className="mt-2">
                        <div className="flex items-center mb-2">
                          <Mail className="mr-2 h-5 w-5 text-gray-400" />
                          <p className="text-sm text-gray-500">{selectedClient.email}</p>
                        </div>
                        <div className="flex items-center mb-2">
                          <Calendar className="mr-2 h-5 w-5 text-gray-400" />
                          <p className="text-sm text-gray-500">Joined: {formatDate(selectedClient.created_at)}</p>
                        </div>
                        <div className="flex items-center mb-2">
                          <FileText className="mr-2 h-5 w-5 text-gray-400" />
                          <p className="text-sm text-gray-500">Projects: {selectedClient.projectCount}</p>
                        </div>

                        <div className="mt-4 space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700">Client Actions</h4>
                            <div className="mt-2 grid grid-cols-2 gap-2">
                              <button
                                className="btn btn-outline btn-sm"
                                onClick={() => toast('Feature coming soon!', 'info')}
                              >
                                View Projects
                              </button>
                              <button
                                className="btn btn-outline btn-sm"
                                onClick={() => toast('Feature coming soon!', 'info')}
                              >
                                Send Message
                              </button>
                              <button
                                className="btn btn-outline btn-sm"
                                onClick={() => toast('Feature coming soon!', 'info')}
                              >
                                Edit Details
                              </button>
                              <button
                                className="btn btn-outline btn-sm text-error hover:bg-error hover:text-white"
                                onClick={() => toast('Feature coming soon!', 'info')}
                              >
                                Deactivate
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => setSelectedClient(null)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default ClientManagement;