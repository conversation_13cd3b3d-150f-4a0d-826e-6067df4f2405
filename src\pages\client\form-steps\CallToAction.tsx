import React from 'react';
import { useFormStore } from '../../../stores/formStore';

const CallToAction: React.FC = () => {
  const { formData, updateFormField } = useFormStore();

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Define the primary actions you want visitors to take on your website. Clear calls-to-action (CTAs) guide visitors toward conversions.
      </p>

      <div className="form-group">
        <label htmlFor="primaryCTA" className="form-label">
          Primary Call to Action <span className="text-error">*</span>
        </label>
        <input
          type="text"
          id="primaryCTA"
          className="input w-full"
          placeholder="e.g., Schedule a Consultation, Get a Quote, Shop Now, Sign Up"
          value={formData.primaryCTA}
          onChange={(e) => updateFormField('primaryCTA', e.target.value)}
          required
        />
        <p className="mt-1 text-xs text-gray-500">
          This is the main action you want visitors to take. It will be prominently featured on your site.
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="secondaryCTA" className="form-label">
          Secondary Call to Action
        </label>
        <input
          type="text"
          id="secondaryCTA"
          className="input w-full"
          placeholder="e.g., Learn More, View Portfolio, Contact Us, Subscribe"
          value={formData.secondaryCTA}
          onChange={(e) => updateFormField('secondaryCTA', e.target.value)}
        />
        <p className="mt-1 text-xs text-gray-500">
          This is an alternative action for visitors who aren't ready for your primary CTA.
        </p>
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">CTA Best Practices</h3>
        <p className="text-sm text-gray-600">
          Effective calls-to-action are clear, action-oriented, and create a sense of urgency. Use strong verbs and keep your CTAs concise. Your primary CTA should align with your main business goal (e.g., generating leads, making sales), while your secondary CTA can nurture visitors who aren't ready to commit.
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Examples of Effective CTAs:</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h4 className="text-sm font-medium text-gray-700">For Service Businesses:</h4>
            <ul className="list-disc ml-5 text-sm text-gray-600 space-y-1">
              <li>Book a Free Consultation</li>
              <li>Get Your Custom Quote</li>
              <li>Schedule a Demo</li>
              <li>Start Your Project</li>
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-700">For E-commerce:</h4>
            <ul className="list-disc ml-5 text-sm text-gray-600 space-y-1">
              <li>Shop Now</li>
              <li>Add to Cart</li>
              <li>View Collection</li>
              <li>Claim Your Discount</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="p-4 rounded-lg bg-yellow-50 border border-yellow-200">
        <h3 className="flex items-center text-sm font-medium text-yellow-800 mb-2">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
          </svg>
          CTA Placement Tip
        </h3>
        <p className="text-sm text-yellow-800">
          Your primary CTA will be placed in high-visibility areas (hero section, end of key content sections). The secondary CTA will appear alongside but with less visual emphasis. Both will also be included in your site's navigation/footer for easy access.
        </p>
      </div>
    </div>
  );
};

export default CallToAction;