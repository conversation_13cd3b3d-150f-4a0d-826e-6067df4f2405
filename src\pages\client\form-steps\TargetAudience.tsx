import React, { useState } from 'react';
import { useFormStore } from '../../../stores/formStore';
import { Plus, X } from 'lucide-react';

const TargetAudience: React.FC = () => {
  const { formData, updateFormField } = useFormStore();
  const [newInterest, setNewInterest] = useState('');

  const addInterest = () => {
    if (newInterest && !formData.audienceInterests.includes(newInterest)) {
      updateFormField('audienceInterests', [...formData.audienceInterests, newInterest]);
      setNewInterest('');
    }
  };

  const removeInterest = (interest: string) => {
    updateFormField(
      'audienceInterests',
      formData.audienceInterests.filter(item => item !== interest)
    );
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Understanding your target audience helps us create a website that resonates with the people most likely to use your products or services.
      </p>

      <div className="form-group">
        <label htmlFor="primaryAudience" className="form-label">
          Primary Audience Description <span className="text-error">*</span>
        </label>
        <textarea
          id="primaryAudience"
          className="input w-full h-32"
          placeholder="e.g., Small business owners in the tech industry who are looking to improve their marketing strategies. They typically have 5-20 employees and annual revenue between $500K and $2M."
          value={formData.primaryAudience}
          onChange={(e) => updateFormField('primaryAudience', e.target.value)}
          required
        />
        <p className="mt-1 text-xs text-gray-500">
          Describe your ideal customers in terms of their needs, pain points, and demographics.
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="audienceAge" className="form-label">
          Age Range
        </label>
        <select
          id="audienceAge"
          className="input w-full"
          value={formData.audienceAge}
          onChange={(e) => updateFormField('audienceAge', e.target.value)}
        >
          <option value="">Select an age range</option>
          <option value="under-18">Under 18</option>
          <option value="18-24">18-24</option>
          <option value="25-34">25-34</option>
          <option value="35-44">35-44</option>
          <option value="45-54">45-54</option>
          <option value="55-64">55-64</option>
          <option value="65+">65 and older</option>
          <option value="all-ages">All ages</option>
        </select>
      </div>

      <div className="space-y-3">
        <label className="form-label">Interests & Behaviors</label>
        <p className="text-sm text-gray-600">
          What are your audience's interests, hobbies, or behaviors that relate to your business?
        </p>

        <div className="flex flex-wrap gap-2 mb-3">
          {formData.audienceInterests.map((interest, index) => (
            <span
              key={index}
              className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-sm text-primary"
            >
              {interest}
              <button
                type="button"
                onClick={() => removeInterest(interest)}
                className="ml-1.5 rounded-full p-0.5 text-primary hover:bg-primary/20"
              >
                <X size={14} />
              </button>
            </span>
          ))}
          {formData.audienceInterests.length === 0 && (
            <p className="text-sm text-gray-500 italic">No interests added yet</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="text"
            className="input flex-grow"
            placeholder="e.g., Sustainability, Technology, Fitness"
            value={newInterest}
            onChange={(e) => setNewInterest(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addInterest();
              }
            }}
          />
          <button
            type="button"
            onClick={addInterest}
            className="btn btn-outline btn-sm whitespace-nowrap"
          >
            <Plus size={16} className="mr-1" /> Add
          </button>
        </div>
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Audience-Focused Design</h3>
        <p className="text-sm text-gray-600">
          The more specific you can be about your audience, the more targeted we can make your website.
          Different demographics respond to different design elements, content tones, and calls to action.
          For example, a website targeting seniors might use larger text and simpler navigation, while one
          for tech-savvy millennials might incorporate more interactive elements.
        </p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Example Audience Description:</h3>
        <p className="text-sm text-gray-600">
          "Our primary customers are working parents aged 30-45 with household incomes above $75,000. They value convenience and quality, and are willing to pay premium prices for products that save them time. They're highly educated, tech-savvy, and make most purchasing decisions based on online research and peer recommendations. They're concerned about environmental sustainability and prefer brands that demonstrate social responsibility."
        </p>
      </div>
    </div>
  );
};

export default TargetAudience;