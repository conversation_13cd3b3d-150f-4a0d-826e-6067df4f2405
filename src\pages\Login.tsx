import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../components/ui/Toaster';
import { Layout, Mail, Lock, LogIn } from 'lucide-react';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const navigate = useNavigate();
  const { signIn, userRole, user, loading: authLoading, refreshProfile } = useAuth();
  const { toast } = useToast();

  // Redirect if user is already logged in
  useEffect(() => {
    if (!authLoading && user && userRole) {
      if (userRole === 'admin') {
        navigate('/admin/dashboard');
      } else {
        navigate('/client/dashboard');
      }
    }
  }, [user, userRole, authLoading, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error, role } = await signIn(email, password);

      if (error) {
        toast('Invalid email or password', 'error');
        setLoading(false);
      } else {
        toast('Login successful', 'success');
        setIsRedirecting(true);

        // Use the role returned directly from signIn
        // Add a longer delay to ensure the toast is visible and role is properly loaded
        // Set the role in sessionStorage immediately
        if (role) {
          sessionStorage.setItem('userRole', role);
        }

        setTimeout(async () => {
          // First check the role returned from signIn
          // If that's not available, fall back to the userRole from context
          // If both are unavailable, default to client
          let effectiveRole = role || userRole;

          // If still no role, try refreshing the profile one more time
          if (!effectiveRole) {
            try {
              await refreshProfile();
              effectiveRole = userRole;
            } catch (err) {
              // Handle error silently
            }
          }

          // Default to client if still no role
          effectiveRole = effectiveRole || 'client';

          // Store the role in sessionStorage to ensure it persists
          sessionStorage.setItem('userRole', effectiveRole);

          // Double-check that the role is properly set in sessionStorage
          const storedRole = sessionStorage.getItem('userRole');
          // Force a small delay to ensure sessionStorage is updated
          await new Promise(resolve => setTimeout(resolve, 100));

          if (effectiveRole === 'admin') {
            // Use replace to avoid back button issues
            navigate('/admin/dashboard', { replace: true });
          } else {
            navigate('/client/dashboard', { replace: true });
          }

          setLoading(false);
          setIsRedirecting(false);
        }, 2000); // Increased delay to ensure role is loaded
      }
    } catch (error) {
      toast('An error occurred during login', 'error');
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <Link to="/" className="inline-flex items-center text-xl font-bold text-primary">
            <Layout className="mr-2 h-6 w-6" />
            WebsiteCreator
          </Link>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">Sign in to your account</h2>
          <p className="mt-2 text-sm text-gray-600">
            Or{' '}
            <Link to="/register" className="font-medium text-primary hover:text-primary-dark">
              create a new account
            </Link>
          </p>
        </div>

        <div className="card mt-8">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="form-label">
                Email address
              </label>
              <div className="mt-1 relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                  <Mail size={18} />
                </span>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className="input pl-10 w-full"
                  placeholder="Email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between">
                <label htmlFor="password" className="form-label">
                  Password
                </label>
                <div className="text-sm">
                  <a href="#" className="font-medium text-primary hover:text-primary-dark">
                    Forgot your password?
                  </a>
                </div>
              </div>
              <div className="mt-1 relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                  <Lock size={18} />
                </span>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  className="input pl-10 w-full"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="btn btn-primary w-full flex justify-center items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    {isRedirecting ? 'Redirecting...' : 'Signing in...'}
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign in
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;