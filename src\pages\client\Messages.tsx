import React, { useEffect } from 'react';
import ClientLayout from '../../components/layout/ClientLayout';
import { useMessaging } from '../../contexts/MessagingContext';
import MessageList from '../../components/messaging/MessageList';
import MessageInput from '../../components/messaging/MessageInput';
import ProjectList from '../../components/messaging/ProjectList';
import { MessageSquare } from 'lucide-react';

const ClientMessages: React.FC = () => {
  const {
    messages,
    projects,
    activeProjectId,
    setActiveProjectId,
    sendMessage,
    markAllAsRead,
    loading,
    error
  } = useMessaging();

  // Set the first project as active if none is selected
  useEffect(() => {
    // Do NOT mark all messages as read when the component mounts
    // We only want to mark messages as read when clicking on a specific project

    if (!activeProjectId && projects.length > 0) {
      setActiveProjectId(projects[0].id);
    }
  }, [projects, activeProjectId, setActiveProjectId]);

  const handleSendMessage = (content: string) => {
    if (activeProjectId) {
      sendMessage(activeProjectId, content);
    }
  };

  const activeProject = projects.find(p => p.id === activeProjectId);

  return (
    <ClientLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-gray-900">Messages</h1>
          <p className="mt-1 text-gray-500">
            Communicate with our team about your website projects.
          </p>
        </div>

        {error && (
          <div className="rounded-md bg-red-50 p-4 text-red-700">
            <p>{error}</p>
          </div>
        )}

        <div className="grid h-[calc(100vh-240px)] grid-cols-1 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm lg:grid-cols-4">
          {/* Projects sidebar */}
          <div className="border-r border-gray-200">
            <div className="border-b border-gray-200 p-4">
              <h2 className="text-lg font-medium text-gray-900">Your Projects</h2>
              <p className="text-sm text-gray-500">Select a project to chat about</p>
            </div>
            <ProjectList
              projects={projects}
              activeProjectId={activeProjectId}
              onSelectProject={setActiveProjectId}
              loading={loading && projects.length === 0}
            />
          </div>

          {/* Messages area */}
          <div className="flex flex-col lg:col-span-3">
            {activeProjectId ? (
              <>
                <div className="border-b border-gray-200 p-4">
                  <div className="flex items-center">
                    <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-primary/10 text-primary">
                      <MessageSquare className="h-5 w-5" />
                    </div>
                    <div>
                      <h2 className="text-lg font-medium text-gray-900">{activeProject?.name}</h2>
                      <p className="text-sm text-gray-500">Support Chat</p>
                    </div>
                  </div>
                </div>
                <div className="flex-1 overflow-hidden">
                  <MessageList messages={messages} loading={loading && activeProjectId !== null} />
                </div>
                <MessageInput onSendMessage={handleSendMessage} disabled={!activeProjectId} />
              </>
            ) : (
              <div className="flex h-full flex-col items-center justify-center p-4 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400" />
                <h3 className="mt-4 text-lg font-medium text-gray-900">No project selected</h3>
                <p className="mt-1 text-gray-500">
                  Choose a project from the sidebar to start messaging with our team.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </ClientLayout>
  );
};

export default ClientMessages;
