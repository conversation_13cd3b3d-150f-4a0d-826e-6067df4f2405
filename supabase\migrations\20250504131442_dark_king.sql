/*
  # Initial Schema Setup

  1. New Tables
     - `users` - Extended profile data for users
     - `projects` - Website projects
     - `form_submissions` - Client form submissions
     - `messages` - Communication between clients and admins
     - `prompts` - Generated prompts for bolt.new
  
  2. Security
     - Enable RLS on all tables
     - Add policies for proper data access control
*/

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('client', 'admin')) DEFAULT 'client',
  first_name TEXT,
  last_name TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('draft', 'in_progress', 'review', 'completed')) DEFAULT 'draft',
  progress INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create form_submissions table
CREATE TABLE IF NOT EXISTS form_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  step TEXT NOT NULL,
  data JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  read BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create prompts table
CREATE TABLE IF NOT EXISTS prompts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompts ENABLE ROW LEVEL SECURITY;

-- Policy: Users can read their own data
CREATE POLICY "Users can read own data"
  ON users
  FOR SELECT
  USING (auth.uid() = id);

-- Policy: Users can update their own data
CREATE POLICY "Users can update own data"
  ON users
  FOR UPDATE
  USING (auth.uid() = id);

-- Policy: Clients can read their own projects
CREATE POLICY "Clients can read own projects"
  ON projects
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy: Clients can insert their own projects
CREATE POLICY "Clients can insert own projects"
  ON projects
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy: Clients can update their own projects
CREATE POLICY "Clients can update own projects"
  ON projects
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Policy: Admins can read all projects
CREATE POLICY "Admins can read all projects"
  ON projects
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- Policy: Clients can read their own form submissions
CREATE POLICY "Clients can read own form submissions"
  ON form_submissions
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = form_submissions.project_id AND projects.user_id = auth.uid()
  ));

-- Policy: Clients can insert their own form submissions
CREATE POLICY "Clients can insert own form submissions"
  ON form_submissions
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = form_submissions.project_id AND projects.user_id = auth.uid()
  ));

-- Policy: Clients can update their own form submissions
CREATE POLICY "Clients can update own form submissions"
  ON form_submissions
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = form_submissions.project_id AND projects.user_id = auth.uid()
  ));

-- Policy: Admins can read all form submissions
CREATE POLICY "Admins can read all form submissions"
  ON form_submissions
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- Policy: Users can read messages in their projects
CREATE POLICY "Users can read messages in their projects"
  ON messages
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = messages.project_id AND (projects.user_id = auth.uid() OR EXISTS (
      SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
    ))
  ));

-- Policy: Users can insert messages in their projects
CREATE POLICY "Users can insert messages in their projects"
  ON messages
  FOR INSERT
  WITH CHECK (
    auth.uid() = sender_id AND (
      EXISTS (
        SELECT 1 FROM projects WHERE projects.id = messages.project_id AND projects.user_id = auth.uid()
      ) OR 
      EXISTS (
        SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
      )
    )
  );

-- Policy: Users can update read status of their messages
CREATE POLICY "Users can update read status of their messages"
  ON messages
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = messages.project_id AND (projects.user_id = auth.uid() OR EXISTS (
      SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
    ))
  ));

-- Policy: Admins can read all prompts
CREATE POLICY "Admins can read all prompts"
  ON prompts
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- Policy: Admins can insert prompts
CREATE POLICY "Admins can insert prompts"
  ON prompts
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- Policy: Admins can update prompts
CREATE POLICY "Admins can update prompts"
  ON prompts
  FOR UPDATE
  USING (EXISTS (
    SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
  ));

-- Policy: Clients can read prompts for their projects
CREATE POLICY "Clients can read prompts for their projects"
  ON prompts
  FOR SELECT
  USING (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = prompts.project_id AND projects.user_id = auth.uid()
  ));

-- Create index for common query patterns
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_form_submissions_project_id ON form_submissions(project_id);
CREATE INDEX IF NOT EXISTS idx_messages_project_id ON messages(project_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_prompts_project_id ON prompts(project_id);