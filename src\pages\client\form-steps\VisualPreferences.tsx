import React, { useState, useEffect } from 'react';
import { useFormStore } from '../../../stores/formStore';
import { Plus, Trash2, ExternalLink } from 'lucide-react';

const VisualPreferences: React.FC = () => {
  const { formData, updateFormField } = useFormStore();
  const [newInspirationUrl, setNewInspirationUrl] = useState('');
  const [newCompetitorUrl, setNewCompetitorUrl] = useState('');

  // Ensure arrays are initialized
  useEffect(() => {
    if (!formData.inspirationUrls || !Array.isArray(formData.inspirationUrls)) {
      updateFormField('inspirationUrls', []);
    }

    if (!formData.competitorUrls || !Array.isArray(formData.competitorUrls)) {
      updateFormField('competitorUrls', []);
    }
  }, []);

  const addInspirationUrl = () => {
    if (newInspirationUrl && newInspirationUrl.trim() !== '') {
      // Ensure we're working with an array
      const currentUrls = Array.isArray(formData.inspirationUrls) ? formData.inspirationUrls : [];

      // Only add if not already included
      if (!currentUrls.includes(newInspirationUrl)) {
        const updatedUrls = [...currentUrls, newInspirationUrl];
        updateFormField('inspirationUrls', updatedUrls);
        setNewInspirationUrl('');
      }
    }
  };

  const removeInspirationUrl = (url: string) => {
    // Ensure we're working with an array
    const currentUrls = Array.isArray(formData.inspirationUrls) ? formData.inspirationUrls : [];

    const updatedUrls = currentUrls.filter(item => item !== url);
    updateFormField('inspirationUrls', updatedUrls);
  };

  const addCompetitorUrl = () => {
    if (newCompetitorUrl && newCompetitorUrl.trim() !== '') {
      // Ensure we're working with an array
      const currentUrls = Array.isArray(formData.competitorUrls) ? formData.competitorUrls : [];

      // Only add if not already included
      if (!currentUrls.includes(newCompetitorUrl)) {
        const updatedUrls = [...currentUrls, newCompetitorUrl];
        updateFormField('competitorUrls', updatedUrls);
        setNewCompetitorUrl('');
      }
    }
  };

  const removeCompetitorUrl = (url: string) => {
    // Ensure we're working with an array
    const currentUrls = Array.isArray(formData.competitorUrls) ? formData.competitorUrls : [];

    const updatedUrls = currentUrls.filter(item => item !== url);
    updateFormField('competitorUrls', updatedUrls);
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Help us understand your visual preferences by providing examples of websites you like and information about your competitors.
      </p>

      <div className="form-group">
        <label htmlFor="style" className="form-label">
          Preferred Style <span className="text-error">*</span>
        </label>
        <select
          id="style"
          className="input w-full"
          value={formData.style}
          onChange={(e) => updateFormField('style', e.target.value)}
          required
        >
          <option value="">Select a style</option>
          <option value="modern">Modern & Sleek</option>
          <option value="minimalist">Minimalist</option>
          <option value="bold">Bold & Colorful</option>
          <option value="luxury">Luxury & Elegant</option>
          <option value="playful">Playful & Fun</option>
          <option value="corporate">Professional & Corporate</option>
          <option value="vintage">Vintage or Retro</option>
          <option value="technical">Technical & Detailed</option>
          <option value="earthy">Natural & Earthy</option>
          <option value="artistic">Artistic & Creative</option>
        </select>
      </div>

      <div className="space-y-3">
        <h3 className="text-lg font-medium">Websites You Like</h3>
        <p className="text-sm text-gray-600">
          Share links to websites whose design, layout, or style you admire (they don't have to be in your industry).
        </p>

        <div className="space-y-2">
          {(Array.isArray(formData.inspirationUrls) ? formData.inspirationUrls : []).map((url, index) => (
            <div key={index} className="flex items-center">
              <input
                type="url"
                className="input flex-grow"
                value={url}
                readOnly
              />
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="mx-2 text-primary hover:text-primary-dark"
              >
                <ExternalLink size={16} />
              </a>
              <button
                type="button"
                onClick={() => removeInspirationUrl(url)}
                className="p-1 text-gray-500 hover:text-error"
              >
                <Trash2 size={16} />
              </button>
            </div>
          ))}
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="url"
            className="input flex-grow"
            placeholder="https://example.com"
            value={newInspirationUrl}
            onChange={(e) => setNewInspirationUrl(e.target.value)}
          />
          <button
            type="button"
            onClick={addInspirationUrl}
            onKeyDown={(e) => e.key === 'Enter' && addInspirationUrl()}
            className="btn btn-outline btn-sm whitespace-nowrap"
          >
            <Plus size={16} className="mr-1" /> Add URL
          </button>
        </div>
      </div>

      <div className="space-y-3">
        <h3 className="text-lg font-medium">Competitor Websites</h3>
        <p className="text-sm text-gray-600">
          Share links to your competitors' websites. This helps us understand your industry standards and position your brand effectively.
        </p>

        <div className="space-y-2">
          {(Array.isArray(formData.competitorUrls) ? formData.competitorUrls : []).map((url, index) => (
            <div key={index} className="flex items-center">
              <input
                type="url"
                className="input flex-grow"
                value={url}
                readOnly
              />
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="mx-2 text-primary hover:text-primary-dark"
              >
                <ExternalLink size={16} />
              </a>
              <button
                type="button"
                onClick={() => removeCompetitorUrl(url)}
                className="p-1 text-gray-500 hover:text-error"
              >
                <Trash2 size={16} />
              </button>
            </div>
          ))}
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="url"
            className="input flex-grow"
            placeholder="https://competitor.com"
            value={newCompetitorUrl}
            onChange={(e) => setNewCompetitorUrl(e.target.value)}
          />
          <button
            type="button"
            onClick={addCompetitorUrl}
            onKeyDown={(e) => e.key === 'Enter' && addCompetitorUrl()}
            className="btn btn-outline btn-sm whitespace-nowrap"
          >
            <Plus size={16} className="mr-1" /> Add URL
          </button>
        </div>
      </div>

      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Why Visual Examples Matter</h3>
        <p className="text-sm text-gray-600">
          Providing examples of websites you like gives us clear visual direction for your project. When looking at other websites, consider what specific elements appeal to you: Is it the color scheme? The layout? The typography? The more specific you can be about what you like (and don't like), the better we can match your preferences.
        </p>
      </div>
    </div>
  );
};

export default VisualPreferences;