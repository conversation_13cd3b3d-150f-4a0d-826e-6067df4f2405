Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF81B2E0000 ntdll.dll
7FF819ED0000 KERNEL32.DLL
7FF818470000 KERNELBASE.dll
7FF8154E0000 apphelp.dll
7FF81A030000 USER32.dll
7FF818F40000 win32u.dll
7FF81A420000 GDI32.dll
7FF818C00000 gdi32full.dll
7FF818D40000 msvcp_win.dll
7FF818DF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF819E10000 advapi32.dll
7FF81B110000 msvcrt.dll
7FF8192B0000 sechost.dll
7FF81A300000 RPCRT4.dll
7FF817970000 CRYPTBASE.DLL
7FF818F70000 bcryptPrimitives.dll
7FF81A600000 IMM32.DLL
