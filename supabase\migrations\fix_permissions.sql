-- This migration adds a more permissive policy for form_submissions
-- to help debug the 403 Forbidden error

-- First, drop the existing policy that might be causing issues
DROP POLICY IF EXISTS "Clients can insert own form submissions" ON form_submissions;

-- Create a more permissive policy for inserting form submissions
-- This will allow any authenticated user to insert form submissions
-- You can tighten this later once the basic functionality works
CREATE POLICY "Authenticated users can insert form submissions"
  ON form_submissions
  FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL);

-- If you still have issues, you might want to temporarily disable <PERSON><PERSON> for testing
-- IMPORTANT: Only use this for testing and re-enable RLS before deploying to production
-- ALTER TABLE form_submissions DISABLE ROW LEVEL SECURITY;

-- You can re-enable the original policy once you've fixed the issue:
/*
CREATE POLICY "Clients can insert own form submissions"
  ON form_submissions
  FOR INSERT
  WITH CHECK (EXISTS (
    SELECT 1 FROM projects WHERE projects.id = form_submissions.project_id AND projects.user_id = auth.uid()
  ));
*/
