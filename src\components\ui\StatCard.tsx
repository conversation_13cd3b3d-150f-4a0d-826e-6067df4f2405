import React from 'react';
import { LucideIcon } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'accent' | 'error';
  trend?: number;
  trendLabel?: string;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon: Icon,
  color = 'primary',
  trend,
  trendLabel,
  className = '',
}) => {
  const getColorClass = () => {
    switch (color) {
      case 'primary':
        return 'primary';
      case 'secondary':
        return 'secondary';
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'accent':
        return 'accent';
      case 'error':
        return 'error';
      default:
        return 'primary';
    }
  };
  
  const getIconBgClass = () => {
    switch (color) {
      case 'primary':
        return 'bg-primary-100 text-primary';
      case 'secondary':
        return 'bg-secondary-100 text-secondary';
      case 'success':
        return 'bg-success-light/20 text-success';
      case 'warning':
        return 'bg-warning-light/20 text-warning';
      case 'accent':
        return 'bg-accent-light/20 text-accent';
      case 'error':
        return 'bg-error-light/20 text-error';
      default:
        return 'bg-primary-100 text-primary';
    }
  };
  
  const getTrendClass = () => {
    if (trend === undefined) return '';
    return trend >= 0 ? 'text-success' : 'text-error';
  };
  
  const colorClass = getColorClass();
  const iconBgClass = getIconBgClass();
  const trendClass = getTrendClass();
  
  return (
    <div className={`card-stats ${colorClass} ${className}`}>
      <div className="flex-shrink-0 mr-4">
        <div className={`flex h-12 w-12 items-center justify-center rounded-xl ${iconBgClass}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      <div>
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        <p className={`text-2xl font-bold text-${colorClass}`}>{value}</p>
        {trend !== undefined && (
          <div className={`mt-1 flex items-center text-xs font-medium ${trendClass}`}>
            {trend >= 0 ? '↑' : '↓'} {Math.abs(trend)}%
            {trendLabel && <span className="ml-1 text-gray-500">{trendLabel}</span>}
          </div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
