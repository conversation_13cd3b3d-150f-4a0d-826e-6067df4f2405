import React, { useState } from 'react';
import { useFormStore } from '../../../stores/formStore';
import { GripVertical, X } from 'lucide-react';

const ContentPriority: React.FC = () => {
  const { formData, updateFormField } = useFormStore();
  const [newSection, setNewSection] = useState('');
  
  // Common website sections that users can choose from
  const commonSections = [
    'Hero/Introduction',
    'About Us',
    'Services',
    'Products',
    'Features',
    'Benefits',
    'Team',
    'Testimonials',
    'Portfolio/Gallery',
    'Pricing',
    'FAQ',
    'Blog',
    'Contact',
    'Newsletter Signup',
    'Map/Location',
  ];
  
  // Filter out sections that are already selected
  const availableSections = commonSections.filter(
    section => !formData.contentSections.includes(section)
  );

  const addSection = () => {
    if (newSection && !formData.contentSections.includes(newSection)) {
      updateFormField('contentSections', [...formData.contentSections, newSection]);
      setNewSection('');
    }
  };

  const removeSection = (section: string) => {
    updateFormField(
      'contentSections',
      formData.contentSections.filter(item => item !== section)
    );
  };

  const moveSection = (fromIndex: number, toIndex: number) => {
    if (
      fromIndex < 0 ||
      fromIndex >= formData.contentSections.length ||
      toIndex < 0 ||
      toIndex >= formData.contentSections.length
    ) {
      return;
    }

    const updatedSections = [...formData.contentSections];
    const [movedSection] = updatedSections.splice(fromIndex, 1);
    updatedSections.splice(toIndex, 0, movedSection);
    
    updateFormField('contentSections', updatedSections);
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-600">
        Arrange the sections of your website in order of importance. This will help us structure your one-page website with the most important content prominently featured.
      </p>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Content Sections</h3>
        <p className="text-sm text-gray-600">
          Drag and reorder the sections below to indicate their priority on your website.
        </p>
        
        {formData.contentSections.length > 0 ? (
          <div className="space-y-2">
            {formData.contentSections.map((section, index) => (
              <div 
                key={index} 
                className="flex items-center bg-white rounded-md border border-gray-200 p-3"
              >
                <div className="cursor-move text-gray-400 mr-2">
                  <GripVertical size={20} />
                </div>
                <span className="flex-grow">{section}</span>
                <div className="flex items-center">
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => moveSection(index, index - 1)}
                      className="p-1 text-gray-500 hover:text-primary"
                    >
                      ↑
                    </button>
                  )}
                  {index < formData.contentSections.length - 1 && (
                    <button
                      type="button"
                      onClick={() => moveSection(index, index + 1)}
                      className="p-1 text-gray-500 hover:text-primary"
                    >
                      ↓
                    </button>
                  )}
                  <button
                    type="button"
                    onClick={() => removeSection(section)}
                    className="ml-2 p-1 text-gray-500 hover:text-error"
                  >
                    <X size={18} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500 italic bg-gray-50 p-4 rounded-md border border-gray-200">
            No content sections added yet. Add sections from the list below or create custom ones.
          </p>
        )}
      </div>
      
      <div className="space-y-3">
        <h3 className="text-lg font-medium">Add Content Sections</h3>
        
        <div className="flex flex-wrap gap-2 mb-4">
          {availableSections.map((section, index) => (
            <button
              key={index}
              type="button"
              onClick={() => {
                updateFormField('contentSections', [...formData.contentSections, section]);
              }}
              className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800 hover:bg-primary/10 hover:text-primary"
            >
              + {section}
            </button>
          ))}
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="text"
            className="input flex-grow"
            placeholder="Custom section name"
            value={newSection}
            onChange={(e) => setNewSection(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addSection();
              }
            }}
          />
          <button
            type="button"
            onClick={addSection}
            className="btn btn-outline btn-sm whitespace-nowrap"
          >
            Add Custom
          </button>
        </div>
      </div>
      
      <div className="rounded-lg bg-primary/5 p-4">
        <h3 className="mb-2 font-medium text-primary">Content Priority Tips</h3>
        <p className="text-sm text-gray-600">
          For one-page websites, it's crucial to prioritize content effectively. Place your most important and compelling information near the top where visitors will see it immediately. Consider your visitor's journey: what do they need to know first to be interested, then to be convinced, and finally to take action?
        </p>
      </div>
      
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
        <h3 className="text-sm font-medium text-gray-700 mb-2">Recommended Structure:</h3>
        <ol className="list-decimal ml-5 text-sm text-gray-600 space-y-1">
          <li>Start with a compelling hero section that clearly communicates your value proposition</li>
          <li>Follow with the most important information about your services or products</li>
          <li>Include social proof (testimonials, reviews, or case studies)</li>
          <li>Add supporting information (about, team, process, etc.)</li>
          <li>End with a clear call-to-action and contact information</li>
        </ol>
      </div>
    </div>
  );
};

export default ContentPriority;