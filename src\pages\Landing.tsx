import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Layout, Share2, Code, LayoutTemplate, ArrowRight, CheckCircle, Users, BarChart } from 'lucide-react';

const Landing: React.FC = () => {
  const { user, userRole } = useAuth();

  const features = [
    {
      icon: <Layout className="h-8 w-8 text-primary" />,
      title: 'Streamlined Website Creation',
      description: 'Transform your business information into a professional website with minimal effort.'
    },
    {
      icon: <Share2 className="h-8 w-8 text-primary" />,
      title: 'Collaborative Platform',
      description: 'Easily communicate with designers and track your project progress in real-time.'
    },
    {
      icon: <Code className="h-8 w-8 text-primary" />,
      title: 'AI-Powered Generation',
      description: 'Our system transforms your requirements into optimized prompts for faster development.'
    },
    {
      icon: <LayoutTemplate className="h-8 w-8 text-primary" />,
      title: 'One-Page Focus',
      description: 'Get a complete, professional online presence with a beautiful single-page website.'
    }
  ];

  const steps = [
    {
      number: 1,
      title: 'Create an Account',
      description: 'Sign up and get instant access to our platform'
    },
    {
      number: 2,
      title: 'Fill Out Project Details',
      description: 'Provide information about your business and website needs'
    },
    {
      number: 3,
      title: 'Review & Communicate',
      description: 'Track progress and provide feedback as needed'
    },
    {
      number: 4,
      title: 'Get Your Website',
      description: 'Receive your professional website built specifically for your needs'
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <header className="bg-gradient-to-r from-primary-dark via-primary to-secondary py-16 text-white">
        <div className="container mx-auto max-w-6xl px-6">
          <nav className="mb-16 flex items-center justify-between">
            <div className="flex items-center">
              <Layout className="mr-2 h-6 w-6" />
              <span className="text-xl font-semibold">WebsiteCreator</span>
            </div>
            <div className="space-x-4">
              {user ? (
                <Link
                  to={userRole === 'admin' ? '/admin/dashboard' : '/client/dashboard'}
                  className="btn btn-outline border-white text-gray-800 hover:bg-white hover:text-primary"
                >
                  Dashboard
                </Link>
              ) : (
                <>
                  <Link to="/login" className="text-white hover:text-gray-200">
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="btn btn-outline border-white text-gray-800 hover:bg-white hover:text-primary"
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </nav>

          <div className="grid gap-8 md:grid-cols-2 md:gap-12">
            <div className="space-y-6">
              <h1 className="text-4xl font-bold leading-tight md:text-5xl">
                Create Your Professional Website in Minutes, Not Months
              </h1>
              <p className="text-lg">
                Our streamlined process transforms your business details into a beautiful one-page website through an
                intuitive guided experience.
              </p>
              <div className="flex space-x-4">
                <Link to="/register" className="btn btn-secondary">
                  Get Started <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <a href="#how-it-works" className="btn btn-outline border-white text-gray-800 hover:bg-white hover:text-primary">
                  Learn More
                </a>
              </div>
            </div>
            <div className="relative flex items-center justify-center">
              <div className="relative h-80 w-full overflow-hidden rounded-lg shadow-xl md:h-96">
                <div className="absolute inset-0 bg-gradient-to-br from-secondary/80 to-accent/80 opacity-80"></div>
                <div className="absolute inset-0 flex flex-col items-center justify-center p-6 text-center">
                  <Layout className="mb-4 h-16 w-16" />
                  <h3 className="mb-2 text-2xl font-semibold">Beautiful Websites</h3>
                  <p className="mb-4">Professional designs tailored to your business</p>
                  <div className="flex flex-wrap justify-center gap-2">
                    <span className="rounded-full bg-white/20 px-3 py-1 text-sm">Fast</span>
                    <span className="rounded-full bg-white/20 px-3 py-1 text-sm">Professional</span>
                    <span className="rounded-full bg-white/20 px-3 py-1 text-sm">Affordable</span>
                    <span className="rounded-full bg-white/20 px-3 py-1 text-sm">Customizable</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto max-w-6xl px-6">
          <div className="mb-12 text-center">
            <h2 className="mb-4 text-3xl font-bold">Why Choose Our Platform</h2>
            <p className="mx-auto max-w-2xl text-gray-600">
              We've simplified website creation by focusing on what matters most: getting your business online with a
              professional presence quickly and efficiently.
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => (
              <div key={index} className="card transform transition-transform hover:-translate-y-1">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="mb-2 text-xl font-semibold">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="bg-gray-50 py-16">
        <div className="container mx-auto max-w-6xl px-6">
          <div className="mb-12 text-center">
            <h2 className="mb-4 text-3xl font-bold">How It Works</h2>
            <p className="mx-auto max-w-2xl text-gray-600">
              Our streamlined process makes getting your professional website simple and hassle-free.
            </p>
          </div>

          <div className="relative">
            <div className="absolute left-0 right-0 top-24 hidden h-0.5 bg-gray-200 md:block"></div>
            <div className="grid gap-8 md:grid-cols-4">
              {steps.map((step, index) => (
                <div key={index} className="relative flex flex-col items-center">
                  <div className="step-indicator completed z-10 mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white">
                    {step.number}
                  </div>
                  <h3 className="mb-2 text-center text-xl font-semibold">{step.title}</h3>
                  <p className="text-center text-gray-600">{step.description}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-12 text-center">
            <Link to="/register" className="btn btn-primary">
              Start Building Your Website
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials/Stats */}
      <section className="py-16">
        <div className="container mx-auto max-w-6xl px-6">
          <div className="grid gap-8 md:grid-cols-3">
            <div className="card flex items-center justify-center border-primary bg-primary/5 p-8">
              <div className="text-center">
                <div className="mb-2 flex justify-center">
                  <Users className="h-10 w-10 text-primary" />
                </div>
                <h3 className="mb-1 text-4xl font-bold text-primary">250+</h3>
                <p className="text-gray-700">Happy Clients</p>
              </div>
            </div>
            <div className="card flex items-center justify-center border-secondary bg-secondary/5 p-8">
              <div className="text-center">
                <div className="mb-2 flex justify-center">
                  <Layout className="h-10 w-10 text-secondary" />
                </div>
                <h3 className="mb-1 text-4xl font-bold text-secondary">450+</h3>
                <p className="text-gray-700">Websites Created</p>
              </div>
            </div>
            <div className="card flex items-center justify-center border-accent bg-accent/5 p-8">
              <div className="text-center">
                <div className="mb-2 flex justify-center">
                  <BarChart className="h-10 w-10 text-accent" />
                </div>
                <h3 className="mb-1 text-4xl font-bold text-accent">95%</h3>
                <p className="text-gray-700">Satisfaction Rate</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="bg-gradient-to-r from-accent-dark via-accent to-primary py-16 text-white">
        <div className="container mx-auto max-w-6xl px-6 text-center">
          <h2 className="mb-6 text-3xl font-bold">Ready to Create Your Professional Website?</h2>
          <p className="mx-auto mb-8 max-w-2xl text-lg">
            Join our platform today and transform your business information into a beautiful, functional website in just
            minutes.
          </p>
          <Link to="/register" className="btn btn-secondary">
            Get Started Today
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 py-12 text-gray-300">
        <div className="container mx-auto max-w-6xl px-6">
          <div className="flex flex-col justify-between space-y-6 md:flex-row md:space-y-0">
            <div>
              <div className="mb-4 flex items-center">
                <Layout className="mr-2 h-6 w-6" />
                <span className="text-xl font-semibold text-white">WebsiteCreator</span>
              </div>
              <p className="max-w-md text-sm">
                Transforming business information into professional websites through our streamlined, guided experience.
              </p>
            </div>
            <div className="flex flex-col space-y-6 md:flex-row md:space-x-12 md:space-y-0">
              <div>
                <h3 className="mb-4 text-lg font-semibold text-white">Platform</h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <a href="#" className="hover:text-white">
                      Features
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-white">
                      How It Works
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-white">
                      Pricing
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="mb-4 text-lg font-semibold text-white">Company</h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <a href="#" className="hover:text-white">
                      About Us
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-white">
                      Blog
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-white">
                      Contact
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="mb-4 text-lg font-semibold text-white">Legal</h3>
                <ul className="space-y-2 text-sm">
                  <li>
                    <a href="#" className="hover:text-white">
                      Privacy Policy
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-white">
                      Terms of Service
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div className="mt-8 border-t border-gray-800 pt-8 text-center text-sm">
            <p>© {new Date().getFullYear()} WebsiteCreator. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;