import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { Session, User } from '@supabase/supabase-js';

type UserRole = 'client' | 'admin' | null;

interface UserProfile {
  id: string;
  email: string;
  role: UserRole;
  first_name: string | null;
  last_name: string | null;
  client_name: string | null;
  avatar_url: string | null;
  created_at: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  userRole: UserRole;
  profile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null, role?: UserRole }>;
  signUp: (email: string, password: string, firstName: string, lastName: string, clientName: string, role: UserRole) => Promise<{ error: Error | null, user: User | null }>;
  signOut: () => Promise<void>;
  isAdmin: () => boolean;
  updateProfile: (data: Partial<Omit<UserProfile, 'id' | 'email' | 'role' | 'created_at'>>) => Promise<{ error: Error | null }>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<UserRole>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileFetchInProgress, setProfileFetchInProgress] = useState(false);
  const [initialAuthComplete, setInitialAuthComplete] = useState(false);

  useEffect(() => {
    // Initial session check
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        fetchUserProfile(session.user.id, true);
      } else {
        setLoading(false);
      }

      setInitialAuthComplete(true);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        // Always update session
        setSession(session);

        // Handle different auth events
        if (event === 'SIGNED_IN') {
          setUser(session?.user ?? null);

          if (session?.user && initialAuthComplete) {
            fetchUserProfile(session.user.id, true);
          }
        }
        else if (event === 'SIGNED_OUT') {
          setUser(null);
          setUserRole(null);
          setProfile(null);
          setLoading(false);
        }
        else if (event === 'TOKEN_REFRESHED') {
          // Just update the user, don't refetch profile as this shouldn't change roles
          setUser(session?.user ?? null);
        }
        else {
          // For other events, update user but don't trigger unnecessary profile fetches
          setUser(session?.user ?? null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [initialAuthComplete]);

  const fetchUserProfile = async (userId: string, isAuthEvent = false): Promise<void> => {
    // Prevent concurrent profile fetches
    if (profileFetchInProgress) {
      return;
    }

    try {
      setProfileFetchInProgress(true);

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        // Only reset role if this is an auth event
        if (isAuthEvent) {
          setUserRole(null);
          setProfile(null);
        }
        return;
      }

      if (!data) {
        // Only reset role if this is an auth event
        if (isAuthEvent) {
          setUserRole(null);
          setProfile(null);
        }
        return;
      }

      // Set the profile data
      setProfile(data as UserProfile);

      // Set the user role
      const role = data.role as UserRole;
      setUserRole(role);

      // Store role in sessionStorage for persistence
      if (role) {
        sessionStorage.setItem('userRole', role);
      }
    } catch (error) {
      // Only reset role if this is an auth event
      if (isAuthEvent) {
        setUserRole(null);
        setProfile(null);
      }
    } finally {
      setProfileFetchInProgress(false);
      setLoading(false); // Always set loading to false when profile fetch completes
    }
  };

  const refreshProfile = async (): Promise<void> => {
    if (user) {
      await fetchUserProfile(user.id);
    }
  };

  const updateProfile = async (data: Partial<Omit<UserProfile, 'id' | 'email' | 'role' | 'created_at'>>): Promise<{ error: Error | null }> => {
    try {
      if (!user) {
        return { error: new Error('User not authenticated') };
      }

      const { error } = await supabase
        .from('users')
        .update(data)
        .eq('id', user.id);

      if (error) {
        return { error };
      }

      // Refresh the profile data
      await refreshProfile();
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({ email, password });

      if (!error && data.user) {
        // The onAuthStateChange event will trigger a profile fetch
        // But we'll also do a direct fetch here to get the role immediately

        // Wait for a moment to ensure auth state is updated
        await new Promise(resolve => setTimeout(resolve, 500));

        // Directly query the database for the user role
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', data.user.id)
          .maybeSingle();

        let determinedRole: UserRole = null;

        if (!userError && userData) {
          // Update profile and role directly
          setProfile(userData as UserProfile);
          determinedRole = userData.role as UserRole;
          setUserRole(determinedRole);

          // Store role in sessionStorage for persistence
          if (determinedRole) {
            sessionStorage.setItem('userRole', determinedRole);
          }
        }

        // If we couldn't determine the role, use a fallback
        if (!determinedRole) {
          // Check if the email contains "admin" to provide a fallback
          const isLikelyAdmin = email.toLowerCase().includes('admin');
          determinedRole = isLikelyAdmin ? 'admin' : 'client';
          setUserRole(determinedRole);

          // Store fallback role in sessionStorage
          sessionStorage.setItem('userRole', determinedRole);
        }

        // Return both the error status and the determined role
        return { error, role: determinedRole };
      }

      return { error };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const signUp = async (email: string, password: string, firstName: string, lastName: string, clientName: string, role: UserRole = 'client') => {
    try {
      const { data, error } = await supabase.auth.signUp({ email, password });

      if (!error && data.user) {
        // Create the user profile with role, name, client name, and empty avatar_url
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email,
            role: 'client', // Force 'client' role for new signups
            first_name: firstName,
            last_name: lastName,
            client_name: clientName || null, // Store client name if provided
            avatar_url: null // Initialize with no avatar
          });

        if (profileError) {
          // Sign out the user if profile creation fails
          await supabase.auth.signOut();
          return { error: new Error('Failed to create user profile'), user: null };
        }

        // Fetch the newly created profile
        await fetchUserProfile(data.user.id);
      }

      return { error, user: data.user };
    } catch (error) {
      return { error: error as Error, user: null };
    }
  };

  const signOut = async () => {
    // Clear the sessionStorage
    sessionStorage.removeItem('userRole');
    // Sign out from Supabase
    await supabase.auth.signOut();
  };

  const isAdmin = () => {
    // Check if the userRole is explicitly set to 'admin'
    if (userRole === 'admin') {
      return true;
    }

    // If we have a profile, check its role
    if (profile && profile.role === 'admin') {
      return true;
    }

    // Check sessionStorage as a fallback
    const storedRole = sessionStorage.getItem('userRole');

    if (storedRole === 'admin') {
      // Update the state to match sessionStorage
      if (userRole !== 'admin') {
        setUserRole('admin');
      }
      return true;
    }

    // Otherwise, not an admin
    return false;
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        userRole,
        profile,
        loading,
        signIn,
        signUp,
        signOut,
        isAdmin,
        updateProfile,
        refreshProfile
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};