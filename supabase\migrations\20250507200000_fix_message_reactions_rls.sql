/*
  # Fix Message Reactions RLS Policies

  1. Security Changes
    - Update the trigger function to allow updating both reactions and read fields
    - Clean up duplicate RLS policies
    - Ensure reactions are visible to both sender and receiver
*/

-- Drop duplicate policies
DROP POLICY IF EXISTS "Users can view messages for their projects" ON messages;
DROP POLICY IF EXISTS "Users can insert messages for their projects" ON messages;

-- Update the trigger function to allow updating both reactions and read fields
CREATE OR REPLACE FUNCTION check_message_reactions_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Ensure only the reactions and read fields are being updated
  IF (NEW.id != OLD.id) OR 
     (NEW.project_id != OLD.project_id) OR 
     (NEW.sender_id != OLD.sender_id) OR 
     (NEW.receiver_id != OLD.receiver_id) OR
     (NEW.content != OLD.content) OR 
     (NEW.created_at != OLD.created_at) THEN
    RAISE EXCEPTION 'Only the reactions and read fields can be updated';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update the RLS policy for updating message reactions
DROP POLICY IF EXISTS "Users can update message reactions" ON messages;
CREATE POLICY "Users can update message reactions"
  ON messages
  FOR UPDATE
  USING (
    -- User can update reactions on messages in their projects
    -- Allow both sender and receiver to update reactions
    (auth.uid() = sender_id OR auth.uid() = receiver_id) OR
    -- Allow admins to update any message
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

-- Update the RLS policy for updating read status
DROP POLICY IF EXISTS "Users can update read status of their messages" ON messages;
CREATE POLICY "Users can update read status"
  ON messages
  FOR UPDATE
  USING (
    -- Only the receiver can mark a message as read
    auth.uid() = receiver_id OR
    -- Allow admins to update any message
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );

-- Update the RLS policy for updating own messages
DROP POLICY IF EXISTS "Users can update their own messages" ON messages;
CREATE POLICY "Users can update their own messages"
  ON messages
  FOR UPDATE
  USING (
    -- Only the sender can update their own messages
    auth.uid() = sender_id OR
    -- Allow admins to update any message
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  );
