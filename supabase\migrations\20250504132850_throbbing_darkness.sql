/*
  # Fix Users Table RLS Policies

  1. Security Changes
    - Add RLS policy for inserting new user profiles during signup
    - Policy ensures users can only create their own profile with proper role validation
    - Maintains existing policies for reading and updating own data

  2. Notes
    - Policy allows authenticated users to create their profile during signup
    - Validates that the user ID matches their auth ID
    - Ensures role can only be set to 'client' during signup
*/

-- Add policy for inserting new user profiles during signup
CREATE POLICY "Users can create their own profile during signup"
  ON users
  FOR INSERT
  TO public
  WITH CHECK (
    -- Ensure user can only create their own profile
    auth.uid() = id
    -- Only allow 'client' role during signup
    AND role = 'client'
  );