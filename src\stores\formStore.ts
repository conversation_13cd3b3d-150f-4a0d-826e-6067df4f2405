import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type FormStep = 
  | 'company-basics'
  | 'brand-assets'
  | 'contact-info'
  | 'business-details'
  | 'services-products'
  | 'visual-preferences'
  | 'target-audience'
  | 'call-to-action'
  | 'content-priority';

interface FormState {
  currentStep: FormStep;
  projectId: string | null;
  formData: {
    // Company Basics
    companyName: string;
    industry: string;
    foundingYear: string;
    // Brand Assets
    logo: string; // URL or placeholder
    colorPalette: string[];
    typography: {
      headingFont: string;
      bodyFont: string;
    };
    // Contact Information
    address: string;
    phone: string;
    email: string;
    socialMedia: {
      facebook?: string;
      instagram?: string;
      twitter?: string;
      linkedin?: string;
      youtube?: string;
    };
    // Business Details
    missionStatement: string;
    valueProposition: string;
    // Services/Products
    offerings: Array<{
      name: string;
      description: string;
      price?: string;
      features?: string[];
    }>;
    // Visual Preferences
    style: string; // e.g., 'modern', 'classic', 'minimalist'
    inspirationUrls: string[];
    competitorUrls: string[];
    // Target Audience
    primaryAudience: string;
    audienceAge: string;
    audienceInterests: string[];
    // Call-to-Action
    primaryCTA: string;
    secondaryCTA: string;
    // Content Priority
    contentSections: string[]; // ordered by priority
  };
  setCurrentStep: (step: FormStep) => void;
  setProjectId: (id: string | null) => void;
  updateFormField: <K extends keyof FormState['formData']>(
    field: K, 
    value: FormState['formData'][K]
  ) => void;
  updateNestedFormField: <
    K extends keyof FormState['formData'], 
    N extends keyof FormState['formData'][K]
  >(
    field: K,
    nestedField: N,
    value: FormState['formData'][K][N]
  ) => void;
  reset: () => void;
}

const defaultFormData = {
  // Company Basics
  companyName: '',
  industry: '',
  foundingYear: '',
  // Brand Assets
  logo: '',
  colorPalette: [],
  typography: {
    headingFont: '',
    bodyFont: '',
  },
  // Contact Information
  address: '',
  phone: '',
  email: '',
  socialMedia: {
    facebook: '',
    instagram: '',
    twitter: '',
    linkedin: '',
    youtube: '',
  },
  // Business Details
  missionStatement: '',
  valueProposition: '',
  // Services/Products
  offerings: [{
    name: '',
    description: '',
    price: '',
    features: [],
  }],
  // Visual Preferences
  style: '',
  inspirationUrls: [],
  competitorUrls: [],
  // Target Audience
  primaryAudience: '',
  audienceAge: '',
  audienceInterests: [],
  // Call-to-Action
  primaryCTA: '',
  secondaryCTA: '',
  // Content Priority
  contentSections: [],
};

export const useFormStore = create<FormState>()(
  persist(
    (set) => ({
      currentStep: 'company-basics',
      projectId: null,
      formData: { ...defaultFormData },
      setCurrentStep: (step) => set({ currentStep: step }),
      setProjectId: (id) => set({ projectId: id }),
      updateFormField: (field, value) => 
        set((state) => ({
          formData: {
            ...state.formData,
            [field]: value,
          },
        })),
      updateNestedFormField: (field, nestedField, value) =>
        set((state) => ({
          formData: {
            ...state.formData,
            [field]: {
              ...state.formData[field],
              [nestedField]: value,
            },
          },
        })),
      reset: () => set({
        currentStep: 'company-basics',
        projectId: null,
        formData: { ...defaultFormData },
      }),
    }),
    {
      name: 'website-form-storage',
    }
  )
);