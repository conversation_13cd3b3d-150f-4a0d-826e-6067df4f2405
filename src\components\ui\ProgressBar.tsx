import React from 'react';

interface ProgressBarProps {
  value: number;
  max?: number;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'accent' | 'error';
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  label?: string;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  color = 'primary',
  size = 'md',
  showValue = false,
  label,
  className = '',
}) => {
  const percentage = Math.min(Math.max(0, (value / max) * 100), 100);
  
  const getColorClass = () => {
    switch (color) {
      case 'primary':
        return 'bg-gradient-primary';
      case 'secondary':
        return 'bg-gradient-secondary';
      case 'success':
        return 'bg-gradient-success';
      case 'warning':
        return 'bg-warning';
      case 'accent':
        return 'bg-accent';
      case 'error':
        return 'bg-error';
      default:
        return 'bg-gradient-primary';
    }
  };
  
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'h-1';
      case 'md':
        return 'h-2';
      case 'lg':
        return 'h-3';
      default:
        return 'h-2';
    }
  };
  
  return (
    <div className={className}>
      {(label || showValue) && (
        <div className="flex justify-between mb-1">
          {label && <span className="text-sm font-medium text-gray-700">{label}</span>}
          {showValue && (
            <span className="text-sm text-gray-500">
              {value}/{max} ({Math.round(percentage)}%)
            </span>
          )}
        </div>
      )}
      <div className={`w-full overflow-hidden rounded-full bg-gray-100 ${getSizeClass()}`}>
        <div
          className={`${getColorClass()} ${getSizeClass()} rounded-full transition-all duration-500 ease-out`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
