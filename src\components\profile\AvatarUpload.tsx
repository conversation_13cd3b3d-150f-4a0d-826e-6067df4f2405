import React, { useState, useRef, useEffect } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/Toaster';
import { User, Upload, X, Loader2 } from 'lucide-react';

interface AvatarUploadProps {
  url: string | null;
  userId: string;
  onUploadComplete: (url: string) => void;
  size?: 'sm' | 'md' | 'lg';
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  url,
  userId,
  onUploadComplete,
  size = 'md'
}) => {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [avatarUrl, setAvatarUrl] = useState<string | null>(url);
  const [uploading, setUploading] = useState(false);
  const [hover, setHover] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setAvatarUrl(url);
  }, [url]);

  const uploadAvatar = async (event: React.ChangeEvent<HTMLInputElement>) => {
    try {
      setUploading(true);

      if (!event.target.files || event.target.files.length === 0) {
        throw new Error('You must select an image to upload.');
      }

      const file = event.target.files[0];
      const fileExt = file.name.split('.').pop();
      const filePath = `${userId}/avatar-${Math.random()}.${fileExt}`;

      // Check if file is an image
      if (!file.type.match('image.*')) {
        throw new Error('Please select an image file.');
      }

      // Check file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        throw new Error('File size must be less than 2MB.');
      }

      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      const newAvatarUrl = data.publicUrl;
      setAvatarUrl(newAvatarUrl);
      onUploadComplete(newAvatarUrl);
      toast('Avatar updated successfully', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error uploading avatar';
      toast(errorMessage, 'error');
    } finally {
      setUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Determine size classes
  const sizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-24 w-24'
  };

  const iconSizes = {
    sm: 16,
    md: 24,
    lg: 32
  };

  return (
    <div className="flex flex-col items-center">
      <div
        className="relative"
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
      >
        <div
          className={`${sizeClasses[size]} relative flex items-center justify-center overflow-hidden rounded-full bg-gray-100 ${!uploading && 'cursor-pointer'}`}
          onClick={!uploading ? handleButtonClick : undefined}
        >
          {avatarUrl ? (
            <img
              src={avatarUrl}
              alt="Avatar"
              className="h-full w-full object-cover"
            />
          ) : (
            <User size={iconSizes[size]} className="text-gray-400" />
          )}

          {uploading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <Loader2 className="h-6 w-6 animate-spin text-white" />
            </div>
          )}

          {hover && !uploading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <Upload className="h-6 w-6 text-white" />
            </div>
          )}
        </div>

        {avatarUrl && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              setAvatarUrl(null);
              onUploadComplete('');
            }}
            className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-error text-white"
            disabled={uploading}
          >
            <X size={12} />
          </button>
        )}
      </div>

      <input
        type="file"
        id="avatar"
        ref={fileInputRef}
        accept="image/*"
        onChange={uploadAvatar}
        className="hidden"
        disabled={uploading}
      />

      <p className="mt-2 text-xs text-gray-500">
        {uploading ? 'Uploading...' : 'Click to upload'}
      </p>
    </div>
  );
};

export default AvatarUpload;
