import React, { useEffect, useRef } from 'react';
import { Plus } from 'lucide-react';

interface ReactionSelectorProps {
  onSelectReaction: (emoji: string) => void;
  onClose: () => void;
}

// Common emojis for reactions
const COMMON_EMOJIS = ['👍', '❤️', '😂', '😮', '😢', '🙏'];

// Additional emojis for the expanded view
const ADDITIONAL_EMOJIS = ['👎', '😡', '🎉', '🔥', '👏', '🤔', '😊', '🙌'];

const ReactionSelector: React.FC<ReactionSelectorProps> = ({ onSelectReaction, onClose }) => {
  const [expanded, setExpanded] = React.useState(false);
  const selectorRef = useRef<HTMLDivElement>(null);

  // <PERSON>le clicking outside to close the selector
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Emojis to display based on expanded state
  const emojisToShow = expanded
    ? [...COMMON_EMOJIS, ...ADDITIONAL_EMOJIS]
    : COMMON_EMOJIS;

  return (
    <div
      ref={selectorRef}
      className="absolute -top-14 left-0 z-10 rounded-full bg-white p-2 shadow-lg"
      style={{ transform: 'translateX(-25%)' }}
    >
      <div className="flex items-center space-x-2">
        {emojisToShow.map(emoji => (
          <button
            key={emoji}
            onClick={() => onSelectReaction(emoji)}
            className="transition-transform hover:scale-125 focus:outline-none"
          >
            <span className="text-xl">{emoji}</span>
          </button>
        ))}

        {!expanded && (
          <button
            onClick={() => setExpanded(true)}
            className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none"
          >
            <Plus className="h-4 w-4 text-gray-600" />
          </button>
        )}
      </div>

      {/* Arrow pointing down */}
      <div className="absolute -bottom-2 left-1/2 h-4 w-4 -translate-x-1/2 rotate-45 transform bg-white"></div>
    </div>
  );
};

export default ReactionSelector;
