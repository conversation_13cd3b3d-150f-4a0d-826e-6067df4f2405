/*
  # Allow Admin Users to Create Projects
  
  1. Security Changes
    - Add RLS policy for admin users to create projects where they are the client
    - This allows admin users to create projects for themselves
    - Admin users can act as clients for their own projects
*/

-- Policy: Admins can insert projects for themselves
CREATE POLICY "Admins can insert projects for themselves"
  ON projects
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
    )
  );

-- Policy: <PERSON><PERSON> can update projects for themselves
CREATE POLICY "Admins can update projects for themselves"
  ON projects
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'admin'
    )
  );
