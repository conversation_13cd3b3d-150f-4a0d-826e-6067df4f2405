import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import { useSupabase } from './SupabaseContext';
import { useAuth } from './AuthContext';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface ReactionUser {
  name: string;
  timestamp: string;
}

export interface Reaction {
  count: number;
  emoji: string;
  users: Record<string, ReactionUser>;
}

export interface Message {
  id: string;
  project_id: string;
  sender_id: string;
  receiver_id?: string;
  content: string;
  read: boolean;
  created_at: string;
  sender_email?: string;
  sender_name?: string;
  project_name?: string;
  reactions?: Record<string, Reaction>;
}

export interface Project {
  id: string;
  name: string;
  user_id: string;
  user_email?: string;
  user_name?: string;
  client_name?: string;
  unread_count?: number;
}

interface MessagingContextType {
  messages: Message[];
  projects: Project[];
  unreadCount: number;
  activeProjectId: string | null;
  setActiveProjectId: (id: string | null) => void;
  sendMessage: (projectId: string, content: string) => Promise<void>;
  markAsRead: (messageIds: string[]) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  markProjectMessagesAsRead: (projectId: string) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  removeReaction: (messageId: string, emoji: string) => Promise<void>;
  loading: boolean;
  error: string | null;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const MessagingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { supabase } = useSupabase();
  const { user, isAdmin } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [activeProjectId, setActiveProjectId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Use refs to store subscriptions to avoid re-renders
  const messagesSubscriptionRef = useRef<RealtimeChannel | null>(null);
  const unreadCountsSubscriptionRef = useRef<RealtimeChannel | null>(null);

  // Use ref for projects to avoid dependency issues
  const projectsRef = useRef<Project[]>([]);
  useEffect(() => {
    projectsRef.current = projects;
  }, [projects]);

  // Fetch unread message counts directly from the messages table
  const fetchUnreadCounts = useCallback(async () => {
    if (!user) return;

    try {
      // Get project IDs
      const projectIds = projectsRef.current.map(p => p.id);
      if (!projectIds.length) return;

      // Query unread messages for this user
      const { data, error } = await supabase
        .from('messages')
        .select('id, project_id')
        .in('project_id', projectIds)
        .eq('read', false)
        .eq('receiver_id', user.id);

      if (error) {
        return;
      }

      // Calculate unread counts per project
      const projectUnreadCounts: Record<string, number> = {};
      data.forEach((msg: any) => {
        projectUnreadCounts[msg.project_id] = (projectUnreadCounts[msg.project_id] || 0) + 1;
      });

      // Calculate total unread count
      const totalUnread = data.length;

      // Update projects with unread counts
      setProjects(prevProjects => {
        return prevProjects.map(project => ({
          ...project,
          unread_count: projectUnreadCounts[project.id] || 0
        }));
      });

      // Update total unread count
      setUnreadCount(totalUnread);
    } catch (error) {
      // Silent error handling
    }
  }, [supabase, user]);

  // Set up subscription for messages table
  useEffect(() => {
    if (!user) return;

    const setupMessagesSubscription = () => {
      // Clean up any existing subscription
      if (unreadCountsSubscriptionRef.current) {
        unreadCountsSubscriptionRef.current.unsubscribe();
      }

      // Get all project IDs for this user
      const projectIds = projectsRef.current.map(p => p.id);
      if (projectIds.length === 0) {
        return;
      }

      // Subscribe to messages table for all projects
      const subscription = supabase
        .channel('all_messages')
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages'
          },
          async (payload) => {
            const newMessage = payload.new as Message;

            // Check if this message is for one of our projects
            if (projectIds.includes(newMessage.project_id)) {
              // If the message is not from the current user, update unread counts
              if (newMessage.sender_id !== user.id) {
                // Fetch the latest unread counts
                await fetchUnreadCounts();
              }
            }
          }
        )
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'messages'
          },
          async (payload) => {
            const updatedMessage = payload.new as Message;

            // Check if this message is for one of our projects
            if (projectIds.includes(updatedMessage.project_id)) {
              // If a message was marked as read, update unread counts
              if (updatedMessage.read) {
                // Fetch the latest unread counts
                await fetchUnreadCounts();
              }
            }
          }
        )
        .subscribe();

      unreadCountsSubscriptionRef.current = subscription;
    };

    // Set up a polling mechanism as a fallback
    const pollInterval = setInterval(() => {
      fetchUnreadCounts();
    }, 30000); // Poll every 30 seconds

    setupMessagesSubscription();

    // Initial fetch of unread counts
    fetchUnreadCounts();

    // Cleanup subscription and polling on unmount
    return () => {
      if (unreadCountsSubscriptionRef.current) {
        unreadCountsSubscriptionRef.current.unsubscribe();
        unreadCountsSubscriptionRef.current = null;
      }

      clearInterval(pollInterval);
    };
  }, [user, supabase, fetchUnreadCounts]);

  // Fetch projects that the user can message about
  useEffect(() => {
    if (!user) return;

    const fetchProjects = async () => {
      try {
        setLoading(true);
        let query;

        if (isAdmin()) {
          // Admins can see all projects with user details
          query = supabase
            .from('projects')
            .select(`
              id,
              name,
              user_id,
              users!projects_user_id_fkey (
                email,
                first_name,
                last_name,
                client_name
              )
            `)
            .order('updated_at', { ascending: false });
        } else {
          // Clients can only see their own projects
          query = supabase
            .from('projects')
            .select('id, name, user_id')
            .eq('user_id', user.id)
            .order('updated_at', { ascending: false });
        }

        const { data, error } = await query;

        if (error) {
          throw error;
        }

        // Format projects with user details
        const formattedProjects = data.map((project: any) => ({
          id: project.id,
          name: project.name,
          user_id: project.user_id,
          user_email: project.users?.email || '',
          user_name: project.users ?
            `${project.users.first_name || ''} ${project.users.last_name || ''}`.trim() : '',
          client_name: project.users?.client_name || '',
          unread_count: 0
        }));

        setProjects(formattedProjects);
        setLoading(false);

        // Unread counts will be updated by the realtime subscription
        // No need to explicitly fetch them here
      } catch (error) {
        setError('Failed to load projects');
        setLoading(false);
      }
    };

    fetchProjects();
  }, [user, isAdmin, supabase]);

  // Fetch messages for the active project
  useEffect(() => {
    if (!activeProjectId || !user) return;

    const fetchMessages = async () => {
      try {
        setLoading(true);

        // Get messages for the active project
        // For clients, we'll handle missing user data due to RLS after fetching
        const { data, error } = await supabase
          .from('messages')
          .select(`
            id,
            project_id,
            sender_id,
            content,
            read,
            created_at,
            users!messages_sender_id_fkey (
              email,
              first_name,
              last_name,
              client_name,
              role
            )
          `)
          .eq('project_id', activeProjectId)
          .order('created_at', { ascending: true });

        if (error) {
          throw error;
        }

        // Format messages with sender details, handling cases where user data might be null due to RLS
        const formattedMessages = data.map((msg: any) => ({
          id: msg.id,
          project_id: msg.project_id,
          sender_id: msg.sender_id,
          content: msg.content,
          read: msg.read,
          created_at: msg.created_at,
          sender_email: msg.users?.email || '<EMAIL>',
          sender_name: msg.users
            ? (msg.users.client_name || `${msg.users.first_name || ''} ${msg.users.last_name || ''}`.trim())
            : (msg.sender_id === user.id ? 'You' : 'Admin User'),
        }));

        setMessages(formattedMessages);

        // Do NOT automatically mark messages as read when fetching messages
        // We only want to mark messages as read when the user explicitly clicks on a project

        setLoading(false);
      } catch (error) {
        setError('Failed to load messages');
        setLoading(false);
      }
    };

    fetchMessages();

    // Set up realtime subscription for new messages
    const setupMessagesSubscription = () => {
      // Clean up any existing subscription
      if (messagesSubscriptionRef.current) {
        messagesSubscriptionRef.current.unsubscribe();
      }

      // Subscribe to messages table for the active project
      const subscription = supabase
        .channel(`messages:project_id=eq.${activeProjectId}`)
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'messages',
            filter: `project_id=eq.${activeProjectId}`
          },
          async (payload) => {
            // Handle different events
            if (payload.eventType === 'INSERT') {
              const newMessage = payload.new as Message;

              // Fetch sender details with error handling for RLS policies
              let senderData;
              try {
                const { data, error } = await supabase
                  .from('users')
                  .select('email, first_name, last_name, client_name, role')
                  .eq('id', newMessage.sender_id)
                  .single();

                if (error) {
                  // This is likely due to RLS preventing clients from seeing admin details
                  senderData = null;
                } else {
                  senderData = data;
                }
              } catch (error) {
                senderData = null;
              }

              // If we couldn't get sender data (due to RLS), use fallback values
              const messageWithSender = {
                ...newMessage,
                sender_email: senderData?.email || '<EMAIL>',
                sender_name: senderData
                  ? (senderData.client_name || `${senderData.first_name || ''} ${senderData.last_name || ''}`.trim())
                  : 'Admin User',
              };

              // Add new message to state
              setMessages(prev => [...prev, messageWithSender]);

              // No need to manually update unread counts - the database triggers will handle this
              // and the realtime subscription will update the UI
            } else if (payload.eventType === 'UPDATE') {
              // Update existing message
              const updatedMessage = payload.new as Message;

              setMessages(prev =>
                prev.map(msg =>
                  msg.id === updatedMessage.id ? { ...msg, ...updatedMessage } : msg
                )
              );

              // No need to manually update unread counts - the database triggers will handle this
              // and the realtime subscription will update the UI
            }
          }
        )
        .subscribe();

      messagesSubscriptionRef.current = subscription;
    };

    setupMessagesSubscription();

    // Cleanup subscription on unmount or when activeProjectId changes
    return () => {
      if (messagesSubscriptionRef.current) {
        messagesSubscriptionRef.current.unsubscribe();
        messagesSubscriptionRef.current = null;
      }
    };
  }, [activeProjectId, user, supabase]);

  // Mark messages as read (internal implementation)
  const markMessagesAsRead = async (messageIds: string[]) => {
    if (!messageIds.length || !user) return;

    try {
      // Only mark messages where the user is the receiver
      const { error } = await supabase
        .from('messages')
        .update({ read: true })
        .in('id', messageIds)
        .eq('receiver_id', user.id);

      if (error) {
        throw error;
      }

      // Update local state
      setMessages(prev =>
        prev.map(msg =>
          messageIds.includes(msg.id) && msg.receiver_id === user.id
            ? { ...msg, read: true }
            : msg
        )
      );

      // Fetch updated unread counts
      fetchUnreadCounts();
    } catch (error) {
      // Silent error handling
    }
  };

  // Send a new message
  const sendMessage = async (projectId: string, content: string) => {
    if (!user) return;

    try {
      // Find the project
      const project = projectsRef.current.find(p => p.id === projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Determine the receiver_id based on sender
      let receiver_id;

      if (isAdmin()) {
        // If sender is admin, receiver is the project owner (client)
        receiver_id = project.user_id;
      } else {
        // If sender is client, receiver is an admin
        try {
          // Try using the RPC function to get admin users
          const { data: adminUsers, error: adminError } = await supabase
            .rpc('get_admin_users');

          if (adminError) {
            // Try direct query as fallback
            const { data: directAdmins, error: directError } = await supabase
              .from('users')
              .select('id, email, role')
              .eq('role', 'admin');

            if (directError || !directAdmins || directAdmins.length === 0) {
              // Hardcode a known admin user ID as a last resort
              receiver_id = '970fa25f-e499-48f5-805c-48d1ba03f116'; // Replace with a known admin ID
            } else {
              receiver_id = directAdmins[0].id;
            }
          } else if (!adminUsers || adminUsers.length === 0) {
            // Hardcode a known admin user ID as a last resort
            receiver_id = '970fa25f-e499-48f5-805c-48d1ba03f116'; // Replace with a known admin ID
          } else {
            // Use the first admin user found
            receiver_id = adminUsers[0].id;
          }
        } catch (err) {
          // Fallback to hardcoded admin ID
          receiver_id = '970fa25f-e499-48f5-805c-48d1ba03f116'; // Replace with a known admin ID
        }

        if (!receiver_id) {
          throw new Error('Could not determine admin receiver');
        }
      }

      const newMessage = {
        project_id: projectId,
        sender_id: user.id,
        receiver_id,
        content,
        read: false
      };

      const { error } = await supabase
        .from('messages')
        .insert(newMessage);

      if (error) {
        throw error;
      }

      // No need to update state here as the realtime subscription will handle it
    } catch (error) {
      setError('Failed to send message');
    }
  };

  // Public method for marking messages as read
  const markAsRead = async (messageIds: string[]) => {
    await markMessagesAsRead(messageIds);
  };

  // Function to mark all unread messages as read
  const markAllAsRead = async () => {
    if (!user) return;

    try {
      // Mark all messages where the user is the receiver as read
      const { error } = await supabase
        .from('messages')
        .update({ read: true })
        .eq('receiver_id', user.id)
        .eq('read', false);

      if (error) {
        return;
      }

      // Update local state - set all project unread counts to 0
      setProjects(prevProjects =>
        prevProjects.map(project => ({
          ...project,
          unread_count: 0
        }))
      );

      // Update total unread count
      setUnreadCount(0);

      // Update messages state to mark all as read
      setMessages(prev =>
        prev.map(msg =>
          msg.receiver_id === user.id
            ? { ...msg, read: true }
            : msg
        )
      );

      // Also fetch the latest counts to be sure
      fetchUnreadCounts();
    } catch (error) {
      // Silent error handling
    }
  };

  // Function to mark all unread messages for a specific project as read
  const markProjectMessagesAsRead = async (projectId: string) => {
    if (!user || !projectId) return;

    try {
      // Mark all messages for this project where the user is the receiver as read
      const { error } = await supabase
        .from('messages')
        .update({ read: true })
        .eq('project_id', projectId)
        .eq('receiver_id', user.id)
        .eq('read', false);

      if (error) {
        return;
      }

      // Update local state for this project
      setProjects(prevProjects =>
        prevProjects.map(project =>
          project.id === projectId
            ? { ...project, unread_count: 0 }
            : project
        )
      );

      // Update messages state to mark all as read for this project
      setMessages(prev =>
        prev.map(msg =>
          msg.project_id === projectId && msg.receiver_id === user.id
            ? { ...msg, read: true }
            : msg
        )
      );

      // Set unread count to 0 for this project
      setUnreadCount(prevCount => {
        // Calculate how many unread messages were in this project
        const project = projectsRef.current.find(p => p.id === projectId);
        const projectUnreadCount = project?.unread_count || 0;

        // Subtract that from the total
        return Math.max(0, prevCount - projectUnreadCount);
      });

      // Also fetch the latest counts to be sure
      fetchUnreadCounts();
    } catch (error) {
      // Silent error handling
    }
  };

  // Add an effect to check for unread messages when the URL changes
  useEffect(() => {
    // This will run whenever the component mounts or when the user navigates
    const handleNavigation = () => {
      // Update the unread count when navigating
      fetchUnreadCounts();
    };

    // Set up event listener for URL changes
    window.addEventListener('popstate', handleNavigation);

    // Initial check
    handleNavigation();

    // Clean up
    return () => {
      window.removeEventListener('popstate', handleNavigation);
    };
  }, [fetchUnreadCounts]);

  // Add reaction to a message
  const addReaction = async (messageId: string, emoji: string) => {
    if (!user) return;

    try {
      // Find the message
      const message = messages.find(m => m.id === messageId);
      if (!message) {
        throw new Error('Message not found');
      }

      // Get current reactions or initialize empty object
      const currentReactions = message.reactions || {};

      // Check if this emoji already has a reaction entry
      const emojiReaction = currentReactions[emoji];

      // Create a new reaction user entry
      const reactionUser: ReactionUser = {
        name: user.email || 'User', // Use email as fallback
        timestamp: new Date().toISOString()
      };

      let updatedReaction: Reaction;

      // If this emoji already has reactions
      if (emojiReaction) {
        // Check if user already reacted with this emoji
        const userAlreadyReacted = emojiReaction.users && emojiReaction.users[user.id];

        // If user already reacted, don't add another reaction
        if (userAlreadyReacted) {
          return;
        }

        // Update the existing reaction
        updatedReaction = {
          count: emojiReaction.count + 1,
          emoji: emoji,
          users: {
            ...emojiReaction.users,
            [user.id]: reactionUser
          }
        };
      } else {
        // Create a new reaction for this emoji
        updatedReaction = {
          count: 1,
          emoji: emoji,
          users: {
            [user.id]: reactionUser
          }
        };
      }

      // Update the reactions object
      const updatedReactions = {
        ...currentReactions,
        [emoji]: updatedReaction
      };

      // Update the message in the database
      const { error } = await supabase
        .from('messages')
        .update({ reactions: updatedReactions })
        .eq('id', messageId);

      if (error) {
        throw error;
      }

      // Update local state
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, reactions: updatedReactions }
            : msg
        )
      );
    } catch (error) {
      setError('Failed to add reaction');
    }
  };

  // Remove reaction from a message
  const removeReaction = async (messageId: string, emoji: string) => {
    if (!user) return;

    try {
      // Find the message
      const message = messages.find(m => m.id === messageId);
      if (!message || !message.reactions) {
        return;
      }

      // Get current reaction for this emoji
      const emojiReaction = message.reactions[emoji];

      // If no reaction for this emoji or user hasn't reacted, do nothing
      if (!emojiReaction || !emojiReaction.users || !emojiReaction.users[user.id]) {
        return;
      }

      // Create updated reactions object
      const updatedReactions = { ...message.reactions };

      // If this is the last user reacting with this emoji, remove the emoji entry
      if (emojiReaction.count <= 1) {
        delete updatedReactions[emoji];
      } else {
        // Otherwise, remove just this user's reaction
        const updatedUsers = { ...emojiReaction.users };
        delete updatedUsers[user.id];

        updatedReactions[emoji] = {
          count: emojiReaction.count - 1,
          emoji: emoji,
          users: updatedUsers
        };
      }

      // Update the message in the database
      const { error } = await supabase
        .from('messages')
        .update({ reactions: updatedReactions })
        .eq('id', messageId);

      if (error) {
        throw error;
      }

      // Update local state
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, reactions: updatedReactions }
            : msg
        )
      );
    } catch (error) {
      setError('Failed to remove reaction');
    }
  };

  return (
    <MessagingContext.Provider
      value={{
        messages,
        projects,
        unreadCount,
        activeProjectId,
        setActiveProjectId,
        sendMessage,
        markAsRead,
        markAllAsRead,
        markProjectMessagesAsRead,
        addReaction,
        removeReaction,
        loading,
        error
      }}
    >
      {children}
    </MessagingContext.Provider>
  );
};

export const useMessaging = () => {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};
