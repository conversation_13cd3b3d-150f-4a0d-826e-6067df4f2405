import React, { useState } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/Toaster';
import { User, Mail, Briefcase, Save, Loader2 } from 'lucide-react';
import AvatarUpload from './AvatarUpload';

interface ProfileFormProps {
  userId: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  clientName: string | null;
  avatarUrl: string | null;
  isAdmin?: boolean;
  onProfileUpdate: (data: {
    first_name: string;
    last_name: string;
    client_name?: string;
    avatar_url: string;
  }) => Promise<void>;
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  userId,
  email,
  firstName,
  lastName,
  clientName,
  avatarUrl,
  isAdmin = false,
  onProfileUpdate
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    first_name: firstName || '',
    last_name: lastName || '',
    client_name: clientName || '',
    avatar_url: avatarUrl || ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      
      await onProfileUpdate({
        first_name: formData.first_name,
        last_name: formData.last_name,
        client_name: !isAdmin ? formData.client_name : undefined,
        avatar_url: formData.avatar_url
      });
      
      toast('Profile updated successfully', 'success');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast('Failed to update profile', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = (url: string) => {
    setFormData(prev => ({ ...prev, avatar_url: url }));
  };

  return (
    <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm">
      <div className="mb-6 flex items-center">
        <AvatarUpload
          url={formData.avatar_url}
          userId={userId}
          onUploadComplete={handleAvatarUpload}
          size="lg"
        />
        <div className="ml-6">
          <h2 className="text-xl font-semibold text-gray-900">Your Profile</h2>
          <p className="text-sm text-gray-500">
            Update your personal information and profile picture
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="form-group">
          <label htmlFor="email" className="form-label">
            Email Address
          </label>
          <div className="relative">
            <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
              <Mail size={18} />
            </span>
            <input
              type="email"
              id="email"
              value={email}
              className="input w-full pl-10"
              disabled
            />
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Your email address cannot be changed
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="form-group">
            <label htmlFor="first_name" className="form-label">
              First Name
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                <User size={18} />
              </span>
              <input
                type="text"
                id="first_name"
                name="first_name"
                value={formData.first_name}
                onChange={handleChange}
                className="input w-full pl-10"
                placeholder="Your first name"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="last_name" className="form-label">
              Last Name
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                <User size={18} />
              </span>
              <input
                type="text"
                id="last_name"
                name="last_name"
                value={formData.last_name}
                onChange={handleChange}
                className="input w-full pl-10"
                placeholder="Your last name"
              />
            </div>
          </div>
        </div>

        {!isAdmin && (
          <div className="form-group">
            <label htmlFor="client_name" className="form-label">
              Company / Organization Name
            </label>
            <div className="relative">
              <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                <Briefcase size={18} />
              </span>
              <input
                type="text"
                id="client_name"
                name="client_name"
                value={formData.client_name}
                onChange={handleChange}
                className="input w-full pl-10"
                placeholder="Your company or organization name"
              />
            </div>
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <button
            type="submit"
            className="btn btn-primary flex items-center"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProfileForm;
