import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useAuth } from '../../contexts/AuthContext';
import ClientLayout from '../../components/layout/ClientLayout';
import {
  FileText,
  Plus,
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  ArrowUpRight,
  Activity,
  Calendar
} from 'lucide-react';

// Import our new UI components
import Chart from '../../components/ui/Chart';
import ProgressBar from '../../components/ui/ProgressBar';
import StatCard from '../../components/ui/StatCard';

interface Project {
  id: string;
  name: string;
  status: 'draft' | 'in_progress' | 'review' | 'completed';
  progress: number;
  created_at: string;
  updated_at: string;
}

const ClientDashboard: React.FC = () => {
  const { supabase } = useSupabase();
  const { user, userRole } = useAuth();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);



  useEffect(() => {
    const fetchProjects = async () => {
      if (!user) return;

      try {
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .eq('user_id', user.id)
          .order('updated_at', { ascending: false });

        if (error) throw error;
        setProjects(data || []);
      } catch (error) {
        // Handle error silently
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [supabase, user]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Clock className="h-5 w-5 text-gray-500" />;
      case 'in_progress':
        return <AlertTriangle className="h-5 w-5 text-warning" />;
      case 'review':
        return <Info className="h-5 w-5 text-secondary" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-success" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Draft';
      case 'in_progress':
        return 'In Progress';
      case 'review':
        return 'Under Review';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Generate some sample data for charts
  const generateChartData = () => {
    return Array.from({ length: 7 }, () => Math.floor(Math.random() * 100) + 20);
  };

  const weeklyData = generateChartData();
  const weekLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  // Calculate completion percentage
  const completedCount = projects.filter((p) => p.status === 'completed').length;
  const inProgressCount = projects.filter((p) => p.status === 'in_progress' || p.status === 'review').length;
  const completionPercentage = projects.length > 0 ? Math.round((completedCount / projects.length) * 100) : 0;

  return (
    <ClientLayout>
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight text-gray-900">Welcome Back!</h1>
            <p className="mt-1 text-gray-500">
              Manage your website projects and track progress.
            </p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <StatCard
            title="Total Websites"
            value={projects.length}
            icon={FileText}
            color="primary"
          />

          <StatCard
            title="Completed"
            value={completedCount}
            icon={CheckCircle}
            color="success"
            trend={completionPercentage}
            trendLabel="completion rate"
          />

          <StatCard
            title="In Progress"
            value={inProgressCount}
            icon={Clock}
            color="warning"
          />
        </div>

        {/* Activity Overview */}
        <div className="grid gap-6 lg:grid-cols-2">
          <div className="card-gradient">
            <div className="chart-header">
              <div>
                <h3 className="chart-title">Weekly Activity</h3>
                <p className="text-sm text-gray-500">Website updates and progress</p>
              </div>
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10">
                <Activity className="h-4 w-4 text-primary" />
              </div>
            </div>
            <Chart
              data={weeklyData}
              labels={weekLabels}
              height={180}
              color="primary"
              showLabels={true}
            />
          </div>

          <div className="card-gradient">
            <div className="chart-header">
              <div>
                <h3 className="chart-title">Project Timeline</h3>
                <p className="text-sm text-gray-500">Estimated completion dates</p>
              </div>
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-secondary/10">
                <Calendar className="h-4 w-4 text-secondary" />
              </div>
            </div>

            <div className="mt-4 space-y-4">
              {projects.filter(p => p.status !== 'completed').slice(0, 3).map((project, index) => (
                <div key={project.id} className="flex items-center rounded-xl bg-gray-50 p-4">
                  <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-xl bg-secondary/10">
                    <FileText className="h-5 w-5 text-secondary" />
                  </div>
                  <div className="flex-grow">
                    <h3 className="font-medium">{project.name}</h3>
                    <div className="mt-1">
                      <ProgressBar
                        value={project.progress}
                        max={100}
                        color="secondary"
                        size="sm"
                      />
                    </div>
                  </div>
                  <div className="ml-4 text-right">
                    <span className="text-xs font-medium text-gray-500">Est. completion</span>
                    <p className="text-sm font-medium text-gray-900">
                      {new Date(new Date().getTime() + (30 - project.progress / 100 * 30) * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                      })}
                    </p>
                  </div>
                </div>
              ))}

              {projects.filter(p => p.status !== 'completed').length === 0 && (
                <div className="flex justify-center py-8 text-gray-500">
                  No active projects
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="card-gradient">
          <div className="chart-header">
            <h3 className="chart-title">Your Websites</h3>
            <Link to="/client/form" className="flex items-center text-sm font-medium text-primary">
              <span>Create New</span>
              <Plus className="ml-1 h-4 w-4" />
            </Link>
          </div>

          {loading ? (
            <div className="mt-4 flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : projects.length === 0 ? (
            <div className="mt-4 flex flex-col items-center justify-center rounded-xl bg-gray-50 py-12">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                <FileText className="h-8 w-8 text-primary" />
              </div>
              <h3 className="mt-4 mb-2 text-lg font-medium text-gray-900">No websites yet</h3>
              <p className="mb-6 max-w-md text-center text-gray-500">
                Get started by creating your first website project. Our easy-to-use form will guide you through the process.
              </p>
              <Link to="/client/form" className="btn btn-primary">
                Create Website
              </Link>
            </div>
          ) : (
            <div className="mt-4 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-100 rounded-xl">
                <thead>
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Progress
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                    >
                      Last Updated
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 bg-white">
                  {projects.map((project) => (
                    <tr key={project.id} className="transition-colors hover:bg-gray-50">
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="flex items-center">
                          <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10">
                            <FileText className="h-4 w-4 text-primary" />
                          </div>
                          <div className="font-medium text-gray-900">{project.name}</div>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          project.status === 'completed'
                            ? 'bg-success/10 text-success'
                            : project.status === 'review'
                            ? 'bg-secondary/10 text-secondary'
                            : project.status === 'in_progress'
                            ? 'bg-warning/10 text-warning'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {getStatusIcon(project.status)}
                          <span className="ml-1">{getStatusText(project.status)}</span>
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="w-full max-w-xs">
                          <ProgressBar
                            value={project.progress}
                            max={100}
                            color={
                              project.progress === 100
                                ? 'success'
                                : project.progress > 50
                                ? 'secondary'
                                : 'primary'
                            }
                            size="sm"
                            showValue={true}
                          />
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {formatDate(project.updated_at)}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                        <Link
                          to={`/client/form?project=${project.id}`}
                          className="inline-flex items-center rounded-lg bg-primary/10 px-3 py-1 text-xs font-medium text-primary transition-colors hover:bg-primary/20"
                        >
                          Continue
                          <ArrowUpRight className="ml-1 h-3 w-3" />
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </ClientLayout>
  );
};

export default ClientDashboard;