import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { CheckCircle, AlertTriangle, XCircle, X, Info } from 'lucide-react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

interface Toast {
  id: string;
  message: string;
  type: ToastType;
}

interface ToastContextType {
  toast: (message: string, type: ToastType) => void;
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

const generateId = () => Math.random().toString(36).substring(2, 9);

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = (message: string, type: ToastType) => {
    const newToast: Toast = {
      id: generateId(),
      message,
      type,
    };
    setToasts((prev) => [...prev, newToast]);
  };

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  useEffect(() => {
    // Auto-remove toasts after 5 seconds
    const timers = toasts.map((toast) => {
      return setTimeout(() => {
        removeToast(toast.id);
      }, 5000);
    });

    return () => {
      timers.forEach((timer) => clearTimeout(timer));
    };
  }, [toasts]);

  return (
    <ToastContext.Provider value={{ toast }}>
      {children}
      <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-3">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`flex items-center rounded-xl p-4 shadow-elevated transition-all duration-300 animate-slide-up ${
              toast.type === 'success'
                ? 'bg-white border-l-4 border-l-success'
                : toast.type === 'error'
                ? 'bg-white border-l-4 border-l-error'
                : toast.type === 'warning'
                ? 'bg-white border-l-4 border-l-warning'
                : 'bg-white border-l-4 border-l-secondary'
            }`}
          >
            <div className={`mr-3 flex h-8 w-8 items-center justify-center rounded-lg ${
              toast.type === 'success'
                ? 'bg-success/10 text-success'
                : toast.type === 'error'
                ? 'bg-error/10 text-error'
                : toast.type === 'warning'
                ? 'bg-warning/10 text-warning'
                : 'bg-secondary/10 text-secondary'
            }`}>
              {toast.type === 'success' ? (
                <CheckCircle className="h-4 w-4" />
              ) : toast.type === 'error' ? (
                <XCircle className="h-4 w-4" />
              ) : toast.type === 'warning' ? (
                <AlertTriangle className="h-4 w-4" />
              ) : (
                <Info className="h-4 w-4" />
              )}
            </div>
            <div className="flex-1">
              <p className={`font-medium ${
                toast.type === 'success'
                  ? 'text-success'
                  : toast.type === 'error'
                  ? 'text-error'
                  : toast.type === 'warning'
                  ? 'text-warning'
                  : 'text-secondary'
              }`}>
                {toast.type.charAt(0).toUpperCase() + toast.type.slice(1)}
              </p>
              <p className="text-sm text-gray-600">{toast.message}</p>
            </div>
            <button
              onClick={() => removeToast(toast.id)}
              className="ml-4 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

// Create a standalone Toaster component for use in App
export const Toaster = () => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    if (!document.getElementById('toast-container')) {
      const container = document.createElement('div');
      container.id = 'toast-container';
      document.body.appendChild(container);
      const root = createRoot(container);
      root.render(<ToastProvider>{null}</ToastProvider>);
    }
  }, []);

  if (!isMounted) return null;
  return null;
};

// Export a utility function to show toast from anywhere
export const toast = {
  success: (message: string) => {
    const toastContainer = document.getElementById('toast-container');
    if (toastContainer) {
      const toastContext = React.createContext<ToastContextType | undefined>(undefined);
      const { toast } = React.useContext(toastContext) || {};
      toast && toast(message, 'success');
    }
  },
  error: (message: string) => {
    const toastContainer = document.getElementById('toast-container');
    if (toastContainer) {
      const toastContext = React.createContext<ToastContextType | undefined>(undefined);
      const { toast } = React.useContext(toastContext) || {};
      toast && toast(message, 'error');
    }
  },
  warning: (message: string) => {
    const toastContainer = document.getElementById('toast-container');
    if (toastContainer) {
      const toastContext = React.createContext<ToastContextType | undefined>(undefined);
      const { toast } = React.useContext(toastContext) || {};
      toast && toast(message, 'warning');
    }
  },
  info: (message: string) => {
    const toastContainer = document.getElementById('toast-container');
    if (toastContainer) {
      const toastContext = React.createContext<ToastContextType | undefined>(undefined);
      const { toast } = React.useContext(toastContext) || {};
      toast && toast(message, 'info');
    }
  },
};