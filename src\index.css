@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400 transition-colors;
  }
}

@layer components {
  /* Button styles */
  .btn {
    @apply rounded-xl px-5 py-2.5 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-sm;
  }

  .btn-primary {
    @apply bg-primary text-white hover:shadow-colored focus:ring-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:shadow-colored focus:ring-secondary;
  }

  .btn-accent {
    @apply bg-accent text-white hover:bg-accent-dark hover:shadow-colored focus:ring-accent;
  }

  .btn-outline {
    @apply border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hover:border-primary hover:text-primary focus:ring-primary;
  }

  .btn-icon {
    @apply inline-flex items-center justify-center rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-primary;
  }

  /* Input styles */
  .input {
    @apply rounded-xl border border-gray-200 px-4 py-3 shadow-sm transition-all duration-200 focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary;
  }

  /* Card styles */
  .card {
    @apply rounded-2xl border border-gray-100 bg-white p-6 shadow-card transition-all duration-300 hover:shadow-elevated;
  }

  .card-gradient {
    @apply rounded-2xl bg-white p-6 shadow-card relative overflow-hidden;
  }

  .card-gradient::before {
    @apply content-[''] absolute top-0 left-0 w-full h-1 bg-primary;
  }

  .card-stats {
    @apply rounded-2xl border-0 bg-white p-6 shadow-card relative overflow-hidden flex items-center;
  }

  .card-stats::before {
    @apply content-[''] absolute top-0 left-0 w-1 h-full rounded-l-2xl;
  }

  .card-stats.primary::before {
    @apply bg-primary;
  }

  .card-stats.secondary::before {
    @apply bg-secondary;
  }

  .card-stats.success::before {
    @apply bg-success;
  }

  .card-stats.warning::before {
    @apply bg-warning;
  }

  .card-stats.accent::before {
    @apply bg-accent;
  }

  /* Form styles */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }

  .form-error {
    @apply mt-1 text-sm text-error;
  }

  /* Dashboard components */
  .stat-value {
    @apply text-2xl font-bold;
  }

  .stat-label {
    @apply text-sm text-gray-500;
  }

  .progress-bar {
    @apply h-2 w-full overflow-hidden rounded-full bg-gray-100;
  }

  .progress-bar-value {
    @apply h-full rounded-full;
  }

  /* Icon containers */
  .icon-container {
    @apply flex h-10 w-10 items-center justify-center rounded-xl;
  }

  .icon-container-primary {
    @apply bg-primary-100 text-primary;
  }

  .icon-container-secondary {
    @apply bg-secondary-100 text-secondary;
  }

  .icon-container-success {
    @apply bg-success-light/20 text-success;
  }

  .icon-container-warning {
    @apply bg-warning-light/20 text-warning;
  }

  .icon-container-accent {
    @apply bg-accent-light/20 text-accent;
  }
}

/* Step indicators */
.step-indicator {
  @apply relative flex h-12 w-12 items-center justify-center rounded-full border-2 text-sm font-medium shadow-sm transition-all duration-200;
}

.step-indicator.completed {
  @apply border-success bg-success text-white shadow-md;
}

.step-indicator.active {
  @apply border-primary bg-primary text-white shadow-colored;
}

.step-indicator.upcoming {
  @apply border-gray-200 bg-white text-gray-500;
}

.step-connector {
  @apply absolute top-6 h-1 w-full -translate-y-1/2 bg-gray-200 rounded-full;
}

.step-connector.completed {
  @apply bg-success;
}

/* Chart components */
.chart-container {
  @apply rounded-2xl bg-white p-6 shadow-card h-full;
}

.chart-header {
  @apply flex items-center justify-between mb-4;
}

.chart-title {
  @apply text-lg font-medium text-gray-800;
}

/* Data visualization */
.data-grid {
  @apply grid gap-6;
}

.data-card {
  @apply rounded-2xl bg-white p-6 shadow-card flex flex-col;
}

.data-card-header {
  @apply flex items-center justify-between mb-4;
}

.data-card-title {
  @apply text-lg font-medium text-gray-800;
}

.data-card-value {
  @apply text-3xl font-bold text-gray-900;
}

.data-card-description {
  @apply text-sm text-gray-500 mt-1;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

.pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}